<?php

namespace App\Helpers;

use App\Models\Coupon;
use Illuminate\Support\Str;

class UniqueCoupon
{
    public static function generate(int $length = 8, string $prefix = '', int $maxAttempts = 10): string
    {
        $attempt = 0;

        do {
            $randomCode = strtoupper(Str::random($length));
            $fullCode = $prefix.$randomCode;

            $exists = Coupon::where('code', $fullCode)->exists();
            $attempt++;
        } while ($exists && $attempt < $maxAttempts);

        if ($exists) {
            throw new \Exception("Failed to generate unique coupon code after {$maxAttempts} attempts.");
        }

        return $fullCode;
    }
}
