<?php

namespace App\Helpers;

use Illuminate\Support\Facades\DB;

class UniqueNumber
{
    /**
     * Generate a unique numeric code without duplication.
     *
     * @param  string  $table  Table name where the code should be unique
     * @param  string  $column  Column name where the code is stored
     * @param  int  $length  Length of the numeric code (e.g., 6 = 000123)
     * @param  int  $maxAttempts  Max attempts to find unique code before failing
     *
     * @throws \Exception
     */
    public static function generate(string $table, string $column, int $length = 7, int $maxAttempts = 10): string
    {
        $attempt = 0;

        do {
            $attempt++;
            $number = str_pad(random_int(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);

            $exists = DB::table($table)->where($column, $number)->exists();

            if (! $exists) {
                return $number;
            }
        } while ($attempt < $maxAttempts);

        throw new \Exception("Failed to generate unique number after {$maxAttempts} attempts.");
    }
}
