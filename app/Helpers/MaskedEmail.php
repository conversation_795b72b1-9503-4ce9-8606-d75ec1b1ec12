<?php

namespace App\Helpers;

class MaskedEmail
{
    /**
     * Mask an email address for privacy - copy of the implementation in DrawCampaign.php
     *
     * @param  string  $email  The email address to mask
     * @return string The masked email address
     */
    public static function generate(string $email): string
    {
        // Handle empty emails
        if (empty($email)) {
            return '[email protected]';
        }

        // Split email into username and domain parts
        $emailParts = explode('@', $email);

        // Handle invalid email format (no @ symbol)
        if (count($emailParts) !== 2) {
            // Return a generic masked placeholder for invalid emails
            return 'in****id';
        }

        $username = $emailParts[0];
        $domain = $emailParts[1];

        // Handle empty username or domain
        if (empty($username) || empty($domain)) {
            return 'in****id';
        }

        // Mask username part - show first 2 chars and last char, rest as asterisks
        $usernameLength = strlen($username);

        // Handle different username lengths consistently
        $maskedUsername = ($usernameLength <= 3) ? substr($username, 0, 1).str_repeat('*', $usernameLength - 1) : substr($username, 0, 2).str_repeat('*', $usernameLength - 3).substr($username, -1);

        return "$maskedUsername@$domain";
    }
}
