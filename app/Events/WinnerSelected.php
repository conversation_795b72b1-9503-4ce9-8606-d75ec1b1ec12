<?php

namespace App\Events;

use App\Models\Winner;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WinnerSelected implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $winner;

    public function __construct(Winner $winner)
    {
        // Mask email before broadcasting
        $winner->maskEmail();
        $this->winner = $winner;
    }

    public function broadcastOn()
    {
        return new Channel('campaign.'.$this->winner->campaign_id);
    }

    public function broadcastAs()
    {
        return 'winner.selected';
    }
}
