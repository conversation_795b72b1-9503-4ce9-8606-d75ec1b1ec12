<?php

namespace App\Console\Commands;

use App\Mail\CampaignCoupons;
use App\Models\Campaign;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {email=<EMAIL>}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test campaign coupon email to verify email configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("Sending test email to: {$email}");

        // Create a test campaign
        $campaign = new Campaign([
            'name' => 'Test Campaign Email',
            'description' => 'This is a test campaign to verify email functionality',
            'email_subject' => '🧪 Test Email for [NAME] - [CAMPAIGN_NAME]',
            'email_body' => 'Halo [NAME],

Ini adalah email test untuk memverifikasi sistem email campaign.

Anda mendapat [COUPON_COUNT] kupon test:
[COUPONS]

📅 Tanggal Transaksi: [TRANSACTION_DATE]
🎯 Campaign: [CAMPAIGN_NAME]
📝 Deskripsi: [CAMPAIGN_DESCRIPTION]

⏰ Periode Campaign: [START_DATE] - [END_DATE]
🎊 Pengumuman Pemenang: [DRAW_DATE]

Jika Anda menerima email ini, berarti konfigurasi email sudah benar!

Salam,
Tim Viding.co',
            'start_date' => Carbon::now()->subDays(10),
            'end_date' => Carbon::now()->addDays(10),
            'draw_date' => Carbon::now()->addDays(15),
            'status' => 'active',
            'running' => true,
            'max_winners' => 1,
            'coupon_threshold' => 300000,
        ]);

        // Create test coupons
        $coupons = [
            (object) [
                'id' => 1,
                'code' => 'TEST12345',
                'transaction_id' => 999999,
                'campaign_id' => 1,
            ],
            (object) [
                'id' => 2,
                'code' => 'TEST67890',
                'transaction_id' => 999999,
                'campaign_id' => 1,
            ],
        ];

        // Create test user
        $user = new \stdClass;
        $user->name = 'Test User';
        $user->email = $email;
        $user->transaction_date = Carbon::now()->subDays(5);

        try {
            // Send the email
            Mail::to($email)->send(new CampaignCoupons($user, $coupons, $campaign));
            
            $this->info("✅ Test email sent successfully!");
            $this->line("Check Mailpit at: http://localhost:8025");
            $this->line("Subject: " . $campaign->replacePlaceholders($campaign->email_subject, [
                'user_name' => $user->name,
                'coupon_count' => count($coupons),
            ]));
            
        } catch (\Exception $e) {
            $this->error("❌ Failed to send test email:");
            $this->error($e->getMessage());
            
            $this->line("");
            $this->info("Troubleshooting steps:");
            $this->line("1. Check if Mailpit is running: ps aux | grep mailpit");
            $this->line("2. Check Mailpit web interface: http://localhost:8025");
            $this->line("3. Verify .env mail configuration");
            $this->line("4. Check Laravel logs: tail -f storage/logs/laravel.log");
        }

        return 0;
    }
}
