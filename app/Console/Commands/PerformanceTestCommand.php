<?php

namespace App\Console\Commands;

use App\Helpers\MaskedEmail;
use App\Models\Campaign;
use App\Models\Coupon;
use App\Models\Transaction;
use App\Models\Winner;
use App\Services\DrawService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PerformanceTestCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:performance-test
                            {--scenario=all : Specific test scenario to run (all, draw, coupon, email, ui)}
                            {--size=medium : Dataset size (small, medium, large, very-large)}
                            {--output=console : Output format (console, csv)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run performance tests for the LuckyDraw Engine with large datasets';

    /**
     * Dataset sizes for different test scenarios
     */
    protected $dataSizes = [
        'small' => [
            'transactions' => 100,
            'coupons' => 100,
            'winners' => 10,
        ],
        'medium' => [
            'transactions' => 500,
            'coupons' => 500,
            'winners' => 50,
        ],
        'large' => [
            'transactions' => 1000,
            'coupons' => 1000,
            'winners' => 100,
        ],
        'very-large' => [
            'transactions' => 2000,
            'coupons' => 2000,
            'winners' => 200,
        ],
    ];

    /**
     * Results storage
     */
    protected $results = [];

    /**
     * Execute the console command.
     */
    public function handle(DrawService $drawService)
    {
        // Enable query logging for performance metrics
        DB::enableQueryLog();

        $scenario = $this->option('scenario');
        $size = $this->option('size');
        $output = $this->option('output');

        if (! isset($this->dataSizes[$size])) {
            $this->error('Invalid size option. Choose from: small, medium, large, very-large');

            return 1;
        }

        $this->info('Performance Testing for LuckyDraw Engine');
        $this->info("=======================================\n");

        $this->info('Test Configuration:');
        $this->info("- Scenario: $scenario");
        $this->info("- Dataset Size: $size");
        $this->info("- Output Format: $output\n");

        // Create test campaign if it doesn't exist
        $campaign = $this->createTestCampaign();

        // Generate test data based on the selected size
        $dataSize = $this->dataSizes[$size];
        $this->info('Generating test data...');
        $this->generateTestData($campaign, $dataSize);
        $this->info("Test data generated successfully.\n");

        // Run the selected test scenario
        switch ($scenario) {
            case 'draw':
                $this->testDrawPerformance($campaign, $drawService);
                break;
            case 'coupon':
                $this->testCouponGenerationPerformance($campaign);
                break;
            case 'email':
                $this->testEmailMaskingPerformance($campaign);
                break;
            case 'ui':
                $this->testUIResponsePerformance($campaign);
                break;
            case 'all':
            default:
                $this->testDrawPerformance($campaign, $drawService);
                $this->testCouponGenerationPerformance($campaign);
                $this->testEmailMaskingPerformance($campaign);
                $this->testUIResponsePerformance($campaign);
                break;
        }

        // Output results
        if ($output === 'csv') {
            $this->outputResultsToCSV();
        } else {
            $this->outputResultsToConsole();
        }

        return 0;
    }

    /**
     * Create a test campaign for performance testing
     */
    protected function createTestCampaign(): Campaign
    {
        $campaign = Campaign::firstOrCreate(
            ['name' => 'Performance Test Campaign'],
            [
                'start_date' => Carbon::now()->subDays(30),
                'end_date' => Carbon::now()->addDays(30),
                'draw_date' => Carbon::now(),
                'status' => 'active',
            ]
        );

        return $campaign;
    }

    /**
     * Generate test data for performance testing
     */
    protected function generateTestData(Campaign $campaign, array $dataSize): void
    {
        $bar = $this->output->createProgressBar(3);
        $bar->start();

        // Check if we already have enough test data
        $transactionCount = Transaction::count();
        $couponCount = Coupon::where('campaign_id', $campaign->id)->count();

        // Generate transactions if needed
        if ($transactionCount < $dataSize['transactions']) {
            $this->generateTransactions($dataSize['transactions'] - $transactionCount);
        }
        $bar->advance();

        // Generate coupons if needed
        if ($couponCount < $dataSize['coupons']) {
            $this->generateCoupons($campaign, $dataSize['coupons'] - $couponCount);
        }
        $bar->advance();

        // Generate winners if needed
        $winnerCount = Winner::where('campaign_id', $campaign->id)->count();
        if ($winnerCount < $dataSize['winners']) {
            $this->generateWinners($campaign, $dataSize['winners'] - $winnerCount);
        }
        $bar->advance();

        $bar->finish();
        $this->info("\n");
    }

    /**
     * Generate test transactions
     */
    protected function generateTransactions(int $count): void
    {
        $this->info("\nGenerating $count transactions...");
        // Use smaller batch size for better stability
        $batchSize = 100;
        $batches = ceil($count / $batchSize);

        $bar = $this->output->createProgressBar($batches);
        $bar->start();
        $generatedCount = 0;

        try {
            for ($i = 0; $i < $batches; $i++) {
                $batchCount = min($batchSize, $count - ($i * $batchSize));
                $transactions = [];

                for ($j = 0; $j < $batchCount; $j++) {
                    $transactions[] = [
                        'transaction_id' => 'TX'.uniqid(),
                        'name' => 'Customer '.rand(1000, 9999),
                        'email' => 'customer'.rand(1000, 999999).'@example.com',
                        'amount' => rand(300000, 10000000), // Between Rp300,000 and Rp10,000,000
                        'transaction_date' => Carbon::now()->subDays(rand(1, 30)),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ];
                }

                try {
                    DB::table('transactions')->insert($transactions);
                    $generatedCount += count($transactions);
                } catch (\Exception $e) {
                    $this->warn("Error inserting batch $i: ".$e->getMessage());
                    // Try inserting one by one if batch insert fails
                    foreach ($transactions as $transaction) {
                        try {
                            DB::table('transactions')->insert([$transaction]);
                            $generatedCount++;
                        } catch (\Exception $e2) {
                            // Skip this transaction and continue
                            $this->warn('Error inserting single transaction: '.$e2->getMessage());
                        }
                    }
                }

                $bar->advance();
            }
        } catch (\Exception $e) {
            $this->error('Failed to generate transactions: '.$e->getMessage());
        }

        $bar->finish();
        $this->info("\nGenerated $generatedCount transactions out of $count requested.");
    }

    /**
     * Generate test coupons
     */
    protected function generateCoupons(Campaign $campaign, int $count): void
    {
        $this->info("\nGenerating $count coupons...");
        $batchSize = 100; // Smaller batch size for better stability
        $batches = ceil($count / $batchSize);

        $bar = $this->output->createProgressBar($batches);
        $bar->start();
        $generatedCount = 0;

        // Get transactions that don't have coupons yet
        $transactions = Transaction::whereDoesntHave('coupons')
            ->take($count)
            ->get();

        if ($transactions->count() < $count) {
            $this->warn('Not enough transactions without coupons. Using existing transactions.');
            $transactions = Transaction::inRandomOrder()
                ->take($count)
                ->get();
        }

        $transactionIndex = 0;
        try {
            for ($i = 0; $i < $batches; $i++) {
                $batchCount = min($batchSize, $count - ($i * $batchSize));
                $coupons = [];

                for ($j = 0; $j < $batchCount; $j++) {
                    if ($transactionIndex >= $transactions->count()) {
                        $transactionIndex = 0; // Wrap around if we run out of transactions
                    }

                    $transaction = $transactions[$transactionIndex++];
                    $coupons[] = [
                        'campaign_id' => $campaign->id,
                        'transaction_id' => $transaction->transaction_id,
                        'code' => 'CP'.strtoupper(substr(md5(uniqid()), 0, 8)),
                        'expired' => false,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ];
                }

                try {
                    DB::table('coupons')->insert($coupons);
                    $generatedCount += count($coupons);
                } catch (\Exception $e) {
                    $this->warn("Error inserting batch $i: ".$e->getMessage());
                    // Try inserting one by one if batch insert fails
                    foreach ($coupons as $coupon) {
                        try {
                            DB::table('coupons')->insert([$coupon]);
                            $generatedCount++;
                        } catch (\Exception $e2) {
                            // Skip this coupon and continue
                            $this->warn('Error inserting single coupon: '.$e2->getMessage());
                        }
                    }
                }

                $bar->advance();
            }
        } catch (\Exception $e) {
            $this->error('Failed to generate coupons: '.$e->getMessage());
        }

        $bar->finish();
        $this->info("\nGenerated $generatedCount coupons out of $count requested.");
    }

    /**
     * Generate test winners
     */
    protected function generateWinners(Campaign $campaign, int $count): void
    {
        $this->info("\nGenerating $count winners...");
        $batchSize = 50; // Smaller batch size for better stability
        $batches = ceil($count / $batchSize);

        $bar = $this->output->createProgressBar($batches);
        $bar->start();
        $generatedCount = 0;

        // Get coupons that aren't winners yet
        $coupons = Coupon::where('campaign_id', $campaign->id)
            ->whereDoesntHave('winner')
            ->take($count)
            ->get();

        if ($coupons->count() < $count) {
            $this->warn('Not enough coupons without winners. Using existing coupons.');
            $coupons = Coupon::where('campaign_id', $campaign->id)
                ->inRandomOrder()
                ->take($count)
                ->get();
        }

        $couponIndex = 0;
        try {
            for ($i = 0; $i < $batches; $i++) {
                $batchCount = min($batchSize, $count - ($i * $batchSize));
                $winners = [];

                for ($j = 0; $j < $batchCount; $j++) {
                    if ($couponIndex >= $coupons->count()) {
                        $couponIndex = 0; // Wrap around if we run out of coupons
                    }

                    $coupon = $coupons[$couponIndex++];
                    $transaction = $coupon->transaction;

                    if (! $transaction) {
                        continue; // Skip if transaction is missing
                    }

                    $winners[] = [
                        'campaign_id' => $campaign->id,
                        'coupon_id' => $coupon->id,
                        'name' => $transaction->name,
                        'email' => $transaction->email,
                        'won_at' => Carbon::now()->subDays(rand(1, 30)),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ];
                }

                if (! empty($winners)) {
                    try {
                        DB::table('winners')->insert($winners);
                        $generatedCount += count($winners);
                    } catch (\Exception $e) {
                        $this->warn("Error inserting batch $i: ".$e->getMessage());
                        // Try inserting one by one if batch insert fails
                        foreach ($winners as $winner) {
                            try {
                                DB::table('winners')->insert([$winner]);
                                $generatedCount++;
                            } catch (\Exception $e2) {
                                // Skip this winner and continue
                                $this->warn('Error inserting single winner: '.$e2->getMessage());
                            }
                        }
                    }
                }

                $bar->advance();
            }
        } catch (\Exception $e) {
            $this->error('Failed to generate winners: '.$e->getMessage());
        }

        $bar->finish();
        $this->info("\nGenerated $generatedCount winners out of $count requested.");
    }

    /**
     * Test draw performance
     */
    protected function testDrawPerformance(Campaign $campaign, DrawService $drawService): void
    {
        $this->info("\nTesting Draw Performance");
        $this->info('----------------------');

        // Get the count of eligible coupons
        $eligibleCouponCount = $drawService->getEligibleCouponCount($campaign);
        $this->info("Eligible Coupons: $eligibleCouponCount");

        // Measure performance of random selection
        $this->info('Testing random winner selection...');

        $startMemory = memory_get_usage();
        $startTime = microtime(true);
        $startQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        // Simulate the draw process without actually creating a winner
        DB::beginTransaction();
        try {
            $winner = $drawService->performDraw($campaign);
            DB::rollBack(); // Don't actually save the winner
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error during draw: '.$e->getMessage());
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        $endQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        $executionTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds
        $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2); // Convert to MB
        $queryCount = $endQueryCount - $startQueryCount;

        $this->info("Execution Time: $executionTime ms");
        $this->info("Memory Usage: $memoryUsage MB");
        $this->info("Query Count: $queryCount");

        $this->results[] = [
            'scenario' => 'Draw Performance',
            'dataset_size' => $eligibleCouponCount.' coupons',
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'query_count' => $queryCount,
        ];
    }

    /**
     * Test coupon generation performance
     */
    protected function testCouponGenerationPerformance(Campaign $campaign): void
    {
        $this->info("\nTesting Coupon Generation Performance");
        $this->info('-----------------------------------');

        // Get transactions without coupons for this campaign
        $transactions = Transaction::whereDoesntHave('coupons', function ($query) use ($campaign) {
            $query->where('campaign_id', $campaign->id);
        })->take(1000)->get();

        $transactionCount = $transactions->count();
        $this->info("Transactions to process: $transactionCount");

        if ($transactionCount === 0) {
            $this->warn('No transactions available for coupon generation test.');

            return;
        }

        // Measure performance of coupon generation
        $this->info('Testing coupon generation...');

        $startMemory = memory_get_usage();
        $startTime = microtime(true);
        $startQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        // Simulate the coupon generation process without actually creating coupons
        DB::beginTransaction();
        try {
            foreach ($transactions as $transaction) {
                $couponThreshold = $campaign->coupon_threshold ?? 300000; // Use campaign's threshold or default
                $couponCount = floor($transaction->transaction_sum / $couponThreshold);

                for ($i = 0; $i < $couponCount; $i++) {
                    $code = 'CP'.strtoupper(substr(md5(uniqid()), 0, 8));

                    // Simulate coupon creation without actually inserting
                    $coupon = new Coupon([
                        'campaign_id' => $campaign->id,
                        'transaction_id' => $transaction->transaction_id,
                        'code' => $code,
                        'expired' => false,
                    ]);
                }
            }
            DB::rollBack(); // Don't actually save the coupons
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error during coupon generation: '.$e->getMessage());
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        $endQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        $executionTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds
        $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2); // Convert to MB
        $queryCount = $endQueryCount - $startQueryCount;

        $this->info("Execution Time: $executionTime ms");
        $this->info("Memory Usage: $memoryUsage MB");
        $this->info("Query Count: $queryCount");

        $this->results[] = [
            'scenario' => 'Coupon Generation',
            'dataset_size' => $transactionCount.' transactions',
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'query_count' => $queryCount,
        ];
    }

    /**
     * Test email masking performance
     */
    protected function testEmailMaskingPerformance(Campaign $campaign): void
    {
        $this->info("\nTesting Email Masking Performance");
        $this->info('-------------------------------');

        // Get winners for this campaign
        $winners = Winner::where('campaign_id', $campaign->id)->get();
        $winnerCount = $winners->count();
        $this->info("Winners to process: $winnerCount");

        if ($winnerCount === 0) {
            $this->warn('No winners available for email masking test.');

            return;
        }

        // Measure performance of email masking
        $this->info('Testing email masking...');

        $startMemory = memory_get_usage();
        $startTime = microtime(true);

        // Process all emails
        $maskedEmails = [];
        foreach ($winners as $winner) {
            $maskedEmails[] = MaskedEmail::generate($winner->email);
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $executionTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds
        $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2); // Convert to MB
        $averageTimePerEmail = round($executionTime / $winnerCount, 4); // Average time per email in ms

        $this->info("Execution Time: $executionTime ms");
        $this->info("Memory Usage: $memoryUsage MB");
        $this->info("Average Time Per Email: $averageTimePerEmail ms");

        $this->results[] = [
            'scenario' => 'Email Masking',
            'dataset_size' => $winnerCount.' emails',
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'average_time' => $averageTimePerEmail,
        ];
    }

    /**
     * Test UI response performance
     */
    protected function testUIResponsePerformance(Campaign $campaign): void
    {
        $this->info("\nTesting UI Response Performance");
        $this->info('------------------------------');

        // Get winners for this campaign
        $winnerCount = Winner::where('campaign_id', $campaign->id)->count();
        $this->info("Winners to display: $winnerCount");

        if ($winnerCount === 0) {
            $this->warn('No winners available for UI response test.');

            return;
        }

        // Measure performance of loading winners for display
        $this->info('Testing winner list loading...');

        $startMemory = memory_get_usage();
        $startTime = microtime(true);
        $startQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        // Simulate loading the winners page
        $winners = Winner::where('campaign_id', $campaign->id)
            ->with(['coupon.transaction']) // Eager loading to avoid N+1 query problem
            ->orderBy('won_at', 'desc')
            ->get();

        // Simulate rendering the winners (masking emails, etc.)
        $renderedWinners = [];
        foreach ($winners as $winner) {
            $renderedWinners[] = [
                'name' => $winner->name,
                'email' => MaskedEmail::generate($winner->email),
                'coupon_code' => $winner->coupon->code,
                'won_at' => $winner->won_at->format('M d, Y H:i:s'),
            ];
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        $endQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        $executionTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds
        $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2); // Convert to MB
        $queryCount = $endQueryCount - $startQueryCount;

        $this->info("Execution Time: $executionTime ms");
        $this->info("Memory Usage: $memoryUsage MB");
        $this->info("Query Count: $queryCount");

        $this->results[] = [
            'scenario' => 'UI Response',
            'dataset_size' => $winnerCount.' winners',
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'query_count' => $queryCount,
        ];
    }

    /**
     * Output results to console
     */
    protected function outputResultsToConsole(): void
    {
        $this->info("\nPerformance Test Results");
        $this->info('=======================');

        $headers = ['Scenario', 'Dataset Size', 'Execution Time (ms)', 'Memory Usage (MB)', 'Queries/Avg Time'];
        $rows = [];

        foreach ($this->results as $result) {
            $queryInfo = isset($result['query_count']) ? $result['query_count'] : $result['average_time'].' ms/email';

            $rows[] = [
                $result['scenario'],
                $result['dataset_size'],
                $result['execution_time'],
                $result['memory_usage'],
                $queryInfo,
            ];
        }

        $this->table($headers, $rows);

        $this->info("\nPerformance testing completed at: ".Carbon::now()->toDateTimeString());
    }

    /**
     * Output results to CSV
     */
    protected function outputResultsToCSV(): void
    {
        $filename = 'performance_test_'.Carbon::now()->format('Y-m-d_H-i-s').'.csv';
        $path = storage_path('logs/'.$filename);

        $headers = ['Scenario', 'Dataset Size', 'Execution Time (ms)', 'Memory Usage (MB)', 'Queries/Avg Time', 'Timestamp'];

        $file = fopen($path, 'w');
        fputcsv($file, $headers);

        foreach ($this->results as $result) {
            $queryInfo = isset($result['query_count']) ? $result['query_count'] : $result['average_time'].' ms/email';

            fputcsv($file, [
                $result['scenario'],
                $result['dataset_size'],
                $result['execution_time'],
                $result['memory_usage'],
                $queryInfo,
                Carbon::now()->toDateTimeString(),
            ]);
        }

        fclose($file);

        $this->info("\nPerformance test results saved to: $path");
    }
}
