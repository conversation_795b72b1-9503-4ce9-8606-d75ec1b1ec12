<?php

namespace App\Console\Commands;

use App\Services\DrawService;
use Illuminate\Console\Command;

class CheckCompletedCampaigns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'campaigns:check-completed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and update campaigns that have reached their maximum winners';

    /**
     * Execute the console command.
     */
    public function handle(DrawService $drawService)
    {
        $this->info('Checking for campaigns that have reached their maximum winners...');
        
        $updatedCampaigns = $drawService->checkAndUpdateCompletedCampaigns();
        
        if (empty($updatedCampaigns)) {
            $this->info('No campaigns needed to be updated.');
            return 0;
        }
        
        $this->info(count($updatedCampaigns) . ' campaigns have been marked as completed:');
        
        // Display a table of updated campaigns
        $headers = ['ID', 'Name', 'Winners', 'Max Winners'];
        $rows = [];
        
        foreach ($updatedCampaigns as $campaign) {
            $rows[] = [
                $campaign['id'],
                $campaign['name'],
                $campaign['winner_count'],
                $campaign['max_winners']
            ];
        }
        
        $this->table($headers, $rows);
        
        return 0;
    }
}
