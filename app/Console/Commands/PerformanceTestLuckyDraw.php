<?php

namespace App\Console\Commands;

use App\Helpers\MaskedEmail;
use App\Models\Campaign;
use App\Models\Winner;
use App\Services\DrawService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PerformanceTestLuckyDraw extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-luckydraw-performance
                            {--scenario=all : Specific test scenario to run (all, draw, email, ui)}
                            {--size=small : Dataset size (small, medium, large)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the performance of the LuckyDraw Engine with various dataset sizes';

    /**
     * Dataset sizes for different test scenarios
     */
    protected $dataSizes = [
        'small' => [
            'coupons' => 100,
            'winners' => 10,
        ],
        'medium' => [
            'coupons' => 500,
            'winners' => 50,
        ],
        'large' => [
            'coupons' => 1000,
            'winners' => 100,
        ],
    ];

    /**
     * Results storage
     */
    protected $results = [];

    /**
     * Execute the console command.
     */
    public function handle(DrawService $drawService)
    {
        // Enable query logging for performance metrics
        DB::enableQueryLog();

        $scenario = $this->option('scenario');
        $size = $this->option('size');

        if (! isset($this->dataSizes[$size])) {
            $this->error('Invalid size option. Choose from: small, medium, large');

            return 1;
        }

        $this->info('Performance Testing for LuckyDraw Engine');
        $this->info("=======================================\n");

        $this->info('Test Configuration:');
        $this->info("- Scenario: $scenario");
        $this->info("- Dataset Size: $size\n");

        // Create test campaign if it doesn't exist
        $campaign = $this->createTestCampaign();

        // Run the selected test scenario
        switch ($scenario) {
            case 'draw':
                $this->testDrawPerformance($campaign, $drawService);
                break;
            case 'email':
                $this->testEmailMaskingPerformance($campaign);
                break;
            case 'ui':
                $this->testUIResponsePerformance($campaign);
                break;
            case 'all':
            default:
                $this->testDrawPerformance($campaign, $drawService);
                $this->testEmailMaskingPerformance($campaign);
                $this->testUIResponsePerformance($campaign);
                break;
        }

        // Output results
        $this->outputResultsToConsole();

        return 0;
    }

    /**
     * Create a test campaign for performance testing
     */
    protected function createTestCampaign(): Campaign
    {
        $campaign = Campaign::firstOrCreate(
            ['name' => 'Performance Test Campaign'],
            [
                'start_date' => Carbon::now()->subDays(30),
                'end_date' => Carbon::now()->addDays(30),
                'draw_date' => Carbon::now(),
                'status' => 'active',
            ]
        );

        return $campaign;
    }

    /**
     * Test draw performance
     */
    protected function testDrawPerformance(Campaign $campaign, DrawService $drawService): void
    {
        $this->info("\nTesting Draw Performance");
        $this->info('----------------------');

        // Get the count of eligible coupons
        $eligibleCouponCount = $drawService->getEligibleCouponCount($campaign);
        $this->info("Eligible Coupons: $eligibleCouponCount");

        // Measure performance of random selection
        $this->info('Testing random winner selection...');

        $startMemory = memory_get_usage();
        $startTime = microtime(true);
        $startQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        // Simulate the draw process without actually creating a winner
        DB::beginTransaction();
        try {
            $winner = $drawService->performDraw($campaign);
            DB::rollBack(); // Don't actually save the winner
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error during draw: '.$e->getMessage());
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        $endQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        $executionTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds
        $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2); // Convert to MB
        $queryCount = $endQueryCount - $startQueryCount;

        $this->info("Execution Time: $executionTime ms");
        $this->info("Memory Usage: $memoryUsage MB");
        $this->info("Query Count: $queryCount");

        $this->results[] = [
            'scenario' => 'Draw Performance',
            'dataset_size' => $eligibleCouponCount.' coupons',
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'query_count' => $queryCount,
        ];
    }

    /**
     * Test email masking performance
     */
    protected function testEmailMaskingPerformance(Campaign $campaign): void
    {
        $this->info("\nTesting Email Masking Performance");
        $this->info('-------------------------------');

        // Get winners for this campaign
        $winners = Winner::where('campaign_id', $campaign->id)->get();
        $winnerCount = $winners->count();
        $this->info("Winners to process: $winnerCount");

        if ($winnerCount === 0) {
            $this->warn('No winners available for email masking test. Creating test data...');
            // Create some test winners with emails for testing
            $testEmails = [];
            for ($i = 0; $i < 100; $i++) {
                $testEmails[] = 'test.user'.$i.'@example.com';
            }
            $winners = collect($testEmails);
            $winnerCount = count($testEmails);
            $this->info("Created $winnerCount test emails for masking performance test.");
        }

        // Measure performance of email masking
        $this->info('Testing email masking...');

        $startMemory = memory_get_usage();
        $startTime = microtime(true);

        // Process all emails
        $maskedEmails = [];
        foreach ($winners as $winner) {
            $email = $winner instanceof Winner ? $winner->email : $winner;
            $maskedEmails[] = MaskedEmail::generate($email);
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $executionTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds
        $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2); // Convert to MB
        $averageTimePerEmail = round($executionTime / $winnerCount, 4); // Average time per email in ms

        $this->info("Execution Time: $executionTime ms");
        $this->info("Memory Usage: $memoryUsage MB");
        $this->info("Average Time Per Email: $averageTimePerEmail ms");

        $this->results[] = [
            'scenario' => 'Email Masking',
            'dataset_size' => $winnerCount.' emails',
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'average_time' => $averageTimePerEmail,
        ];
    }

    /**
     * Test UI response performance
     */
    protected function testUIResponsePerformance(Campaign $campaign): void
    {
        $this->info("\nTesting UI Response Performance");
        $this->info('------------------------------');

        // Get winners for this campaign
        $winnerCount = Winner::where('campaign_id', $campaign->id)->count();
        $this->info("Winners to display: $winnerCount");

        if ($winnerCount === 0) {
            $this->warn('No winners available for UI response test. Skipping test.');

            return;
        }

        // Measure performance of loading winners for display
        $this->info('Testing winner list loading...');

        $startMemory = memory_get_usage();
        $startTime = microtime(true);
        $startQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        // Simulate loading the winners page
        $winners = Winner::where('campaign_id', $campaign->id)
            ->with(['coupon.transaction']) // Eager loading to avoid N+1 query problem
            ->orderBy('won_at', 'desc')
            ->get();

        // Simulate rendering the winners (masking emails, etc.)
        $renderedWinners = [];
        foreach ($winners as $winner) {
            $renderedWinners[] = [
                'name' => $winner->name,
                'email' => MaskedEmail::generate($winner->email),
                'coupon_code' => $winner->coupon->code,
                'won_at' => $winner->won_at->format('M d, Y H:i:s'),
            ];
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        $endQueryCount = DB::getQueryLog() ? count(DB::getQueryLog()) : 0;

        $executionTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds
        $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2); // Convert to MB
        $queryCount = $endQueryCount - $startQueryCount;

        $this->info("Execution Time: $executionTime ms");
        $this->info("Memory Usage: $memoryUsage MB");
        $this->info("Query Count: $queryCount");

        $this->results[] = [
            'scenario' => 'UI Response',
            'dataset_size' => $winnerCount.' winners',
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'query_count' => $queryCount,
        ];
    }

    /**
     * Output results to console
     */
    protected function outputResultsToConsole(): void
    {
        $this->info("\nPerformance Test Results");
        $this->info('=======================');

        $headers = ['Scenario', 'Dataset Size', 'Execution Time (ms)', 'Memory Usage (MB)', 'Queries/Avg Time'];
        $rows = [];

        foreach ($this->results as $result) {
            $queryInfo = isset($result['query_count']) ? $result['query_count'] : $result['average_time'].' ms/email';

            $rows[] = [
                $result['scenario'],
                $result['dataset_size'],
                $result['execution_time'],
                $result['memory_usage'],
                $queryInfo,
            ];
        }

        $this->table($headers, $rows);

        $this->info("\nPerformance testing completed at: ".Carbon::now()->toDateTimeString());
        $this->info("\nRecommendations based on results:");
        $this->info('1. Consider adding database indexes for frequently queried columns');
        $this->info('2. Implement caching for winner lists if performance becomes an issue with larger datasets');
        $this->info('3. Use pagination for large winner lists in the UI');
        $this->info('4. Monitor memory usage during peak load periods');
    }
}
