<?php

namespace App\Console\Commands;

use App\Jobs\SendCampaignCouponsJob;
use App\Models\Campaign;
use Illuminate\Console\Command;

class TestCampaignJobCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'campaign:test-job {campaign_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the SendCampaignCouponsJob directly to debug email issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $campaignId = $this->argument('campaign_id');
        
        if (!$campaignId) {
            // Get the latest campaign
            $campaign = Campaign::latest()->first();
            if (!$campaign) {
                $this->error('No campaigns found. Please create a campaign first.');
                return 1;
            }
        } else {
            $campaign = Campaign::find($campaignId);
            if (!$campaign) {
                $this->error("Campaign with ID {$campaignId} not found.");
                return 1;
            }
        }

        $this->info("Testing SendCampaignCouponsJob for campaign: {$campaign->name} (ID: {$campaign->id})");
        
        try {
            // Run the job directly (synchronously) to see any errors
            $job = new SendCampaignCouponsJob($campaign);
            $job->handle();
            
            $this->info("✅ Campaign job completed successfully!");
            $this->line("Check Mailpit at: http://localhost:8025 for emails");
            
        } catch (\Exception $e) {
            $this->error("❌ Campaign job failed:");
            $this->error($e->getMessage());
            $this->line("");
            $this->error("Stack trace:");
            $this->line($e->getTraceAsString());
            
            $this->line("");
            $this->info("Troubleshooting tips:");
            $this->line("1. Check if the campaign has eligible transactions");
            $this->line("2. Verify email configuration in .env");
            $this->line("3. Make sure Mailpit is running");
            $this->line("4. Check if the transactions view exists and has data");
        }

        return 0;
    }
}
