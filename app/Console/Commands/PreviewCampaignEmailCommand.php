<?php

namespace App\Console\Commands;

use App\Mail\CampaignCoupons;
use App\Models\Campaign;
use Carbon\Carbon;
use Illuminate\Console\Command;

class PreviewCampaignEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:preview-campaign-coupons';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Preview the campaign coupons email template';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating preview of campaign coupons email...');

        // Create a mock campaign with custom email content
        $campaign = new Campaign([
            'name' => 'Undian Emas Viding 2024',
            'description' => 'Undian berhadiah emas untuk pengguna setia Viding',
            'email_subject' => '🎉 Selamat [NAME]! Kupon [CAMPAIGN_NAME] Sudah Siap!',
            'email_body' => 'Halo [NAME],

Terima kasih telah berpartisipasi dalam [CAMPAIGN_NAME]! 🎉

Kami dengan senang hati memberikan [COUPON_COUNT] kupon undian khusus untuk Anda:

[COUPONS]

📅 Tanggal Transaksi: [TRANSACTION_DATE]
🎯 Campaign: [CAMPAIGN_NAME]
📝 Deskripsi: [CAMPAIGN_DESCRIPTION]

⏰ Periode Campaign: [START_DATE] - [END_DATE]
🎊 Pengumuman Pemenang: [DRAW_DATE]

Semoga beruntung!

Salam hangat,
Tim Viding.co
https://viding.co',
            'start_date' => Carbon::now()->subDays(30),
            'end_date' => Carbon::now()->addDays(30),
            'draw_date' => Carbon::now()->addDays(45),
            'status' => 'active',
            'running' => true,
            'max_winners' => 5,
            'coupon_threshold' => 300000,
        ]);

        // Create mock coupons
        $coupons = [
            (object) [
                'id' => 1,
                'code' => 'CP12345678',
                'transaction_id' => 87654321,
                'campaign_id' => 1,
            ],
            (object) [
                'id' => 2,
                'code' => 'CP87654321',
                'transaction_id' => 87654321,
                'campaign_id' => 1,
            ],
            (object) [
                'id' => 3,
                'code' => 'CP11223344',
                'transaction_id' => 87654321,
                'campaign_id' => 1,
            ],
        ];

        // Create mock user
        $user = new \stdClass;
        $user->name = 'Budi Santoso';
        $user->email = '<EMAIL>';
        $user->transaction_date = Carbon::now()->subDays(15);

        // Create the mailable
        $mailable = new CampaignCoupons($user, $coupons, $campaign);

        // Get the rendered content
        $envelope = $mailable->envelope();
        $content = $mailable->content();

        $this->info('Email Preview:');
        $this->line('=====================================');
        $this->info('Subject: '.$envelope->subject);
        $this->line('To: '.$user->email);
        $this->line('Template: '.$content->view);
        $this->line('=====================================');

        $this->info('Email content variables:');
        $this->line('- User Name: '.$user->name);
        $this->line('- User Email: '.$user->email);
        $this->line('- Campaign Name: '.$campaign->name);
        $this->line('- Campaign Description: '.$campaign->description);
        $this->line('- Start Date: '.$campaign->start_date->format('d M Y'));
        $this->line('- End Date: '.$campaign->end_date->format('d M Y'));
        $this->line('- Draw Date: '.$campaign->draw_date->format('d M Y'));
        $this->line('- Number of Coupons: '.count($coupons));

        $this->info('Coupon Codes:');
        foreach ($coupons as $index => $coupon) {
            $this->line('  '.($index + 1).'. '.$coupon->code);
        }

        $this->line('=====================================');

        // Show processed email content
        $emailData = $mailable->getEmailData();
        $this->info('Processed Email Body:');
        $this->line('-------------------------------------');
        $this->line($emailData['emailBody']);
        $this->line('-------------------------------------');

        $this->info('Email template successfully previewed!');
        $this->info('The email content is now dynamic and can be customized per campaign in Filament.');

        return 0;
    }
}
