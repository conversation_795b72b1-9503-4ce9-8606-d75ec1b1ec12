<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class VerifyEmailMasking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:verify-email-masking';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify email masking functionality with various email formats';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Email Masking Verification Test');
        $this->info('================================');

        $testCases = [
            ['id' => 'E1', 'description' => 'Standard email', 'email' => '<EMAIL>', 'expected' => 'jo*****<EMAIL>'],
            ['id' => 'E2', 'description' => 'Short username (≤ 3 chars)', 'email' => '<EMAIL>', 'expected' => 'j**@example.com'],
            ['id' => 'E3', 'description' => 'Very long username', 'email' => '<EMAIL>', 'expected' => 'jo*************************<EMAIL>'],
            ['id' => 'E4', 'description' => 'Email with numbers', 'email' => '<EMAIL>', 'expected' => 'jo****<EMAIL>'],
            ['id' => 'E5', 'description' => 'Email with special chars', 'email' => '<EMAIL>', 'expected' => 'jo**********<EMAIL>'],
            ['id' => 'E6', 'description' => 'Email with underscores', 'email' => '<EMAIL>', 'expected' => 'jo***********<EMAIL>'],
            ['id' => 'E7', 'description' => 'Email with dots', 'email' => '<EMAIL>', 'expected' => 'jo***********<EMAIL>'],
            ['id' => 'E8', 'description' => 'International domain', 'email' => 'john.doe@例子.com', 'expected' => 'jo*****e@例子.com'],
            ['id' => 'E9', 'description' => 'Uppercase email', 'email' => '<EMAIL>', 'expected' => 'JO*****<EMAIL>'],
            ['id' => 'E10', 'description' => 'Mixed case email', 'email' => '<EMAIL>', 'expected' => 'Jo*****<EMAIL>'],
            ['id' => 'E11', 'description' => 'Invalid email (no @)', 'email' => 'johndoe', 'expected' => 'in****id'],
            ['id' => 'E12', 'description' => 'Empty email', 'email' => '', 'expected' => '[email protected]'],
        ];

        $headers = ['Test ID', 'Description', 'Email', 'Expected Result', 'Actual Result', 'Status'];
        $rows = [];
        $passCount = 0;
        $failCount = 0;

        foreach ($testCases as $testCase) {
            $actual = \App\Helpers\MaskedEmail::generate($testCase['email']);
            $expected = $testCase['expected'];

            // For test cases where we expect error handling
            if ($expected === 'Error handling?') {
                $status = $actual !== $testCase['email'] ? '✅ PASS' : '❌ FAIL';
            } else {
                $status = $actual === $expected ? '✅ PASS' : '❌ FAIL';
            }

            if (strpos($status, 'PASS') !== false) {
                $passCount++;
            } else {
                $failCount++;
            }

            $rows[] = [
                $testCase['id'],
                $testCase['description'],
                $testCase['email'],
                $expected,
                $actual,
                $status,
            ];
        }

        $this->table($headers, $rows);

        $this->info("\nTest Results: $passCount passed, $failCount failed");

        // Output improved implementation suggestions if there are failures
        if ($failCount > 0) {
            $this->info("\nImprovement Suggestions:");
            $this->info('1. Enhance error handling for invalid emails');
            $this->info('2. Consider consistent handling of short usernames');
            $this->info('3. Ensure proper handling of special characters');
        }
    }
}
