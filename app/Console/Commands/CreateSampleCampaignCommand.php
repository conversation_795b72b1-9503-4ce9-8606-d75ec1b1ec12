<?php

namespace App\Console\Commands;

use App\Models\Campaign;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CreateSampleCampaignCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'campaign:create-sample';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a sample campaign with custom email content';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating sample campaign with custom email content...');

        $campaign = Campaign::create([
            'name' => 'Undian Emas Ramadan 2024',
            'description' => 'Undian spesial bulan <PERSON> dengan hadiah emas 24 karat',
            'email_subject' => '🌙 <PERSON>dan <PERSON>barak [NAME]! Kupon Undian Emas Spesial Menanti!',
            'email_body' => 'Assal<PERSON><PERSON>ai<PERSON><PERSON> [NAME],

<PERSON><PERSON>! 🌙✨

<PERSON><PERSON>, kami sangat bersyukur atas kepercayaan Anda menggunakan layanan Viding.co selama bulan suci Ramadan ini.

<PERSON><PERSON><PERSON> bentuk apresiasi, kami memberikan [COUPON_COUNT] kupon undian emas spesial Ramadan untuk Anda:

🎫 KUPON UNDIAN EMAS RAMADAN:
[COUPONS]

📅 Tanggal Transaksi: [TRANSACTION_DATE]
🕌 Campaign: [CAMPAIGN_NAME]
📖 Deskripsi: [CAMPAIGN_DESCRIPTION]

🎁 HADIAH UTAMA:
🥇 Emas 24 karat senilai Rp 50.000.000
🥈 Emas 22 karat senilai Rp 25.000.000  
🥉 Emas 20 karat senilai Rp 10.000.000

⏰ Periode Campaign: [START_DATE] - [END_DATE]
🎊 Pengumuman Pemenang: [DRAW_DATE] (Live di Instagram @viding.co)

Barakallahu fiikum, semoga berkah dan barokah selalu menyertai Anda.

Salam hangat & Ramadan Mubarak,
Tim Viding.co
🌐 https://viding.co
📱 Follow IG: @viding.co',
            'start_date' => Carbon::now()->subDays(15),
            'end_date' => Carbon::now()->addDays(15),
            'draw_date' => Carbon::now()->addDays(30),
            'status' => 'active',
            'running' => false,
            'max_winners' => 3,
            'coupon_threshold' => 300000,
        ]);

        $this->info("✅ Sample campaign created successfully!");
        $this->line("Campaign ID: {$campaign->id}");
        $this->line("Campaign Name: {$campaign->name}");
        $this->line("Email Subject: {$campaign->email_subject}");
        $this->line("");
        $this->info("You can now:");
        $this->line("1. View the campaign in Filament admin panel");
        $this->line("2. Edit the email content in the 'Email Configuration' section");
        $this->line("3. Start the campaign to send emails to users");
        $this->line("");
        $this->info("Available placeholders for email content:");
        $this->line("- [NAME] - User's name");
        $this->line("- [COUPON_COUNT] - Number of coupons");
        $this->line("- [COUPONS] - HTML formatted coupon codes");
        $this->line("- [TRANSACTION_DATE] - User's transaction date");
        $this->line("- [START_DATE] - Campaign start date");
        $this->line("- [END_DATE] - Campaign end date");
        $this->line("- [DRAW_DATE] - Campaign draw date");
        $this->line("- [CAMPAIGN_NAME] - Campaign name");
        $this->line("- [CAMPAIGN_DESCRIPTION] - Campaign description");

        return 0;
    }
}
