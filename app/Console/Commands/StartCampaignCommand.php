<?php

namespace App\Console\Commands;

use App\Jobs\SendCampaignCouponsJob;
use App\Models\Campaign;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class StartCampaignCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'campaign:start {id? : The ID of the campaign to start}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start a campaign, generate coupons, and send emails to users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $campaignId = $this->argument('id');

        if ($campaignId) {
            // Start a specific campaign
            $campaign = Campaign::find($campaignId);

            if (! $campaign) {
                $this->error("Campaign with ID {$campaignId} not found.");

                return 1;
            }

            $this->startCampaign($campaign);
        } else {
            // Start all active campaigns that are not already running
            $campaigns = Campaign::where('status', 'active')
                ->where('running', false)
                ->where('start_date', '<=', now())
                ->where('end_date', '>=', now())
                ->get();

            if ($campaigns->isEmpty()) {
                $this->info('No campaigns to start.');

                return 0;
            }

            $this->info("Starting {$campaigns->count()} campaigns...");

            foreach ($campaigns as $campaign) {
                // Ensure we're passing a Campaign model, not a Collection
                if ($campaign instanceof Campaign) {
                    $this->startCampaign($campaign);
                } else {
                    $this->error('Invalid campaign object encountered.');
                }
            }
        }

        return 0;
    }

    /**
     * Start a campaign, set it to running, and dispatch the coupon generation job.
     *
     * @return void
     */
    protected function startCampaign(mixed $campaign)
    {
        $this->info("Starting campaign: {$campaign->name} (ID: {$campaign->id})");

        // Update campaign status to running
        $campaign->running = true;
        $campaign->save();

        Log::info("Campaign {$campaign->id} ({$campaign->name}) marked as running");

        // Dispatch the job to generate coupons and send emails
        SendCampaignCouponsJob::dispatch($campaign);

        $this->info("Job dispatched to generate coupons and send emails for campaign {$campaign->id}");
    }
}
