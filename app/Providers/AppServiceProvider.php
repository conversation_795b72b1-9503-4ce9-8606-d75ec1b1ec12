<?php

namespace App\Providers;

use Filament\Actions;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Actions\CreateAction::configureUsing(function (Actions\CreateAction $action) {
            $action->createAnother(false);
        });
    }
}
