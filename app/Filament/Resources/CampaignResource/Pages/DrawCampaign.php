<?php

namespace App\Filament\Resources\CampaignResource\Pages;

use App\Filament\Resources\CampaignResource;
use App\Models\Campaign;
use App\Models\Winner;
use App\Services\DrawService;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Filament\Support\Exceptions\Halt;

class DrawCampaign extends Page
{
    protected static string $resource = CampaignResource::class;

    protected static string $view = 'filament.resources.campaign-resource.pages.draw-campaign';

    protected ?string $heading = 'Draw The Winner';

    public ?Campaign $record = null;

    public ?Winner $winner = null;

    // Flag to indicate if we're in the drawing process
    public bool $isDrawing = false;

    // Flag to indicate if we should hide the winner initially for dramatic reveal
    // Always set to false to ensure winners section is visible by default
    public bool $hideWinnerForReveal = false;

    // Flag to indicate if we're in the loading state (playing sound, preparing reveal)
    public bool $isLoading = false;

    // No pagination needed as winners won't exceed 10

    public function getBreadcrumb(): ?string
    {
        return null;
    }

    public function mount(Campaign $record): void
    {
        // Load the campaign with its winners for accurate counting
        $this->record = $record;
        $this->record->load('winners');

        // Check if this campaign has reached its maximum winners
        $drawService = new DrawService;
        $updatedCampaigns = $drawService->checkAndUpdateCompletedCampaigns();

        // Check if this specific campaign was updated
        $wasUpdated = collect($updatedCampaigns)->contains('id', $this->record->id);
        if ($wasUpdated) {
            // Refresh the record to get updated status
            $this->record->refresh();

            Notification::make()
                ->title('Campaign Completed')
                ->body('This campaign has reached its maximum winners limit and has been marked as completed.')
                ->success()
                ->send();
        }

        // Check if campaign is active
        if ($this->record->status !== 'active') {
            Notification::make()
                ->title('Campaign is not active')
                ->body('Only active campaigns can have draws.')
                ->warning()
                ->send();

            $this->redirect(CampaignResource::getUrl());
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('perform_draw')
                ->label('Start Draw')
                ->color('primary')
                ->icon('heroicon-o-ticket')
                ->requiresConfirmation()
                ->modalHeading('Perform Draw')
                ->modalDescription('Are you sure you want to perform a draw? This will randomly select a winner from all eligible coupons.')
                ->modalSubmitActionLabel('Yes, Perform Draw')
                ->disabled(fn () => $this->record->winners->count() >= $this->record->max_winners)
                ->tooltip(fn () => $this->record->winners->count() >= $this->record->max_winners ?
                    'Maximum winners limit reached ('.$this->record->max_winners.')' : null)
                ->action(function () {
                    $this->performDraw();
                }),
        ];
    }

    public function performDraw(): void
    {
        try {
            $this->isDrawing = true;
            $this->isLoading = true;

            // Set flag to hide only the main winner initially for dramatic reveal
            // This should not affect the recent winners section
            $this->hideWinnerForReveal = true;

            // Reset page to 1 when performing a new draw
            $this->page = 1;

            // Explicitly dispatch the loading event first
            $this->dispatch('draw-loading-started');

            $drawService = new DrawService;

            // Attempt to select a winner
            $winner = $drawService->performDraw($this->record);

            if ($winner) {
                $this->winner = $winner;

                // Don't show the notification immediately - we'll reveal it after the sound
                // First dispatch event to start sound and dramatic reveal sequence
                $this->dispatch('start-draw-sound');
                $this->dispatch('draw-loading-started');

                // The winner will be shown with confetti after the sound plays for a few seconds
                // This is handled by the JavaScript in the blade template
            } else {
                Notification::make()
                    ->title('No Winner')
                    ->body('Failed to select a winner. Please try again.')
                    ->danger()
                    ->send();

                // Reset states since there's no winner to reveal
                $this->resetDrawStates();
            }
        } catch (Halt $exception) {
            // Handle any exceptions from the draw process
            $this->resetDrawStates();

            Notification::make()
                ->title('Draw Halted')
                ->body('The draw process was halted. Please try again.')
                ->warning()
                ->send();
        } catch (\Exception $e) {
            // Handle any other exceptions
            $this->resetDrawStates();

            // Check if this is a max winners limit error
            if (str_contains($e->getMessage(), 'maximum winner limit')) {
                Notification::make()
                    ->title('Maximum Winners Reached')
                    ->body($e->getMessage())
                    ->warning()
                    ->send();
            } else {
                Notification::make()
                    ->title('Error')
                    ->body($e->getMessage())
                    ->danger()
                    ->send();
            }
        } finally {
            $this->isDrawing = false;
            // Note: isLoading will be set to false by JavaScript when the reveal is complete for successful draws
        }
    }

    /**
     * Reset the draw states when an error occurs or no winner is found
     */
    private function resetDrawStates(): void
    {
        $this->hideWinnerForReveal = false;
        $this->isLoading = false;

        // Dispatch an event to hide the loading spinner on the frontend
        $this->dispatch('draw-error-occurred');
    }

    public function getViewData(): array
    {
        $drawService = new DrawService;

        // Get eligible coupon count directly (no cache)
        $eligibleCouponCount = $drawService->getEligibleCouponCount($this->record);

        // Get all winners for this campaign directly (no cache)
        $winners = Winner::where('campaign_id', $this->record->id)
            ->with('coupon') // Eager load the coupon relationship
            ->orderBy('won_at', 'desc')
            ->get();

        // Get eligible transaction count from the campaign model
        $eligibleTransactionCount = $this->record->eligible_transaction_count;

        return [
            'eligibleCouponCount' => $eligibleCouponCount,
            'eligibleTransactionCount' => $eligibleTransactionCount,
            'winners' => $winners,
            'getMaskedEmail' => function (string $email) {
                return $this->getMaskedEmail($email);
            },
        ];
    }
}
