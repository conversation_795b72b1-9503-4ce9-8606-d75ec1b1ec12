<?php

namespace App\Filament\Resources\CampaignResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CouponsRelationManager extends RelationManager
{
    protected static string $relationship = 'coupons';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('code')
                    ->required()
                    ->maxLength(255),

                Forms\Components\Toggle::make('expired')
                    ->required(),

                Forms\Components\Select::make('transaction_id')
                    ->relationship('transaction', 'transaction_id')
                    ->searchable()
                    ->preload()
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('code')
            ->modifyQueryUsing(fn (Builder $query) => $query->where('expired', false))
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->searchable(),

                Tables\Columns\IconColumn::make('expired')
                    ->boolean(),

                Tables\Columns\TextColumn::make('transaction.transaction_id')
                    ->label('Transaction ID')
                    ->sortable(),

                Tables\Columns\TextColumn::make('transaction.name')
                    ->searchable()
                    ->label('Customer'),

                Tables\Columns\TextColumn::make('transaction.email')
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('expired')
                    ->placeholder('All Coupons')
                    ->trueLabel('Expired Coupons')
                    ->falseLabel('Active Coupons')
                    ->queries(
                        true: fn (Builder $query) => $query->where('expired', true),
                        false: fn (Builder $query) => $query->where('expired', false),
                        blank: fn (Builder $query) => $query,
                    ),
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
