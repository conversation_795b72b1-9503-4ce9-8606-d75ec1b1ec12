<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CampaignResource\Pages;
use App\Filament\Resources\CampaignResource\RelationManagers;
use App\Models\Campaign;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CampaignResource extends Resource
{
    protected static ?string $model = Campaign::class;

    protected static ?string $navigationIcon = 'heroicon-o-ticket';

    protected static ?string $navigationLabel = 'Campaigns';

    protected static ?string $modelLabel = 'Campaign';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->columns(3)
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->columnSpanFull()
                    ->placeholder('Enter campaign name')
                    ->maxLength(255),

                Forms\Components\Textarea::make('description')
                    ->placeholder('Enter campaign description')
                    ->maxLength(65535)
                    ->columnSpanFull(),

                Forms\Components\DatePicker::make('start_date')
                    ->required()
                    ->placeholder('Select start date')
                    ->native(false),

                Forms\Components\DatePicker::make('end_date')
                    ->required()
                    ->placeholder('Select end date')
                    ->native(false)
                    ->after('start_date'),

                Forms\Components\DatePicker::make('draw_date')
                    ->required()
                    ->placeholder('Select draw date')
                    ->native(false)
                    ->after('end_date'),

                Forms\Components\TextInput::make('max_winners')
                    ->numeric()
                    ->minValue(1)
                    ->default(1)
                    ->required()
                    ->placeholder('Maximum number of winners')
                    ->helperText('Maximum number of winners allowed for this campaign'),

                Forms\Components\TextInput::make('coupon_threshold')
                    ->numeric()
                    ->minValue(1)
                    ->default(300000)
                    ->required()
                    ->placeholder('Coupon threshold amount')
                    ->helperText('Monetary value required per coupon (e.g., Rp300,000)'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->poll('1s')
            ->deferLoading()
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),

                Tables\Columns\TextColumn::make('start_date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('draw_date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'completed' => 'info',
                        'cancelled' => 'danger',
                    }),

                // Tables\Columns\IconColumn::make('running')
                //     ->boolean()
                //     ->label('Running')
                //     ->trueIcon('heroicon-s-play')
                //     ->falseIcon('heroicon-s-stop')
                //     ->trueColor('success')
                //     ->falseColor('danger'),

                Tables\Columns\TextColumn::make('eligible_transaction_count')
                    ->label('Transactions')
                    ->getStateUsing(fn (Campaign $record): int => $record->eligible_transaction_count)
                    ->badge()
                    ->color('warning'),

                Tables\Columns\TextColumn::make('coupons_count')
                    ->counts('coupons')
                    ->badge()
                    ->color('warning')
                    ->label('Coupons'),

                Tables\Columns\TextColumn::make('winners_count')
                    ->counts('winners')
                    ->badge()
                    ->color('warning')
                    ->label('Winners'),

                Tables\Columns\TextColumn::make('max_winners')
                    ->label('Max Winners')
                    ->badge()
                    ->color('info'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),

                Tables\Filters\Filter::make('upcoming_draws')
                    ->query(fn (Builder $query): Builder => $query->where('draw_date', '>=', now()))
                    ->label('Upcoming Draws'),
            ])
            ->actions([
                Tables\Actions\Action::make('start')
                    ->label('Start')
                    ->icon('heroicon-s-play')
                    ->color('primary')
                    ->visible(fn (Campaign $record): bool => $record->status === 'active' && ! $record->running)
                    ->action(function (Campaign $record) {
                        // Set campaign to running
                        $record->running = true;
                        $record->save();

                        // Dispatch the job to generate coupons and send emails
                        \App\Jobs\SendCampaignCouponsJob::dispatch($record);

                        // Show notification
                        Notification::make()
                            ->title('Campaign Started')
                            ->body('The campaign has been started. Coupons will be generated and emails will be sent to users.')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('stop')
                    ->label('Stop')
                    ->icon('heroicon-s-stop')
                    ->color('danger')
                    ->visible(fn (Campaign $record): bool => $record->running)
                    ->requiresConfirmation()
                    ->action(function (Campaign $record) {
                        // Set campaign to not running
                        $record->running = false;
                        $record->save();

                        // Show notification
                        Notification::make()
                            ->title('Campaign Stopped')
                            ->body('The campaign has been stopped. No more coupons will be generated.')
                            ->warning()
                            ->send();
                    }),

                Tables\Actions\Action::make('cancel')
                    ->label('Cancel')
                    ->icon('heroicon-s-x-circle')
                    ->color('danger')
                    ->visible(fn (Campaign $record): bool => $record->status === 'active' && ! $record->running)
                    ->requiresConfirmation()
                    ->modalHeading('Cancel Campaign')
                    ->modalDescription('Are you sure you want to cancel this campaign? This will stop the campaign and prevent any further draws.')
                    ->modalSubmitActionLabel('Yes, Cancel Campaign')
                    ->action(function (Campaign $record) {
                        // Set campaign to not running
                        $record->status = 'cancelled';
                        $record->save();

                        \App\Jobs\RollbackCoupon::dispatch($record);

                        // Show notification
                        Notification::make()
                            ->title('Campaign Cancelled')
                            ->body('The campaign has been cancelled.')
                            ->danger()
                            ->send();
                    }),

                Tables\Actions\Action::make('draw')
                    ->label('Roll')
                    ->url(fn (Campaign $record): string => static::getUrl('draw', ['record' => $record]))
                    ->icon('heroicon-o-ticket')
                    ->color('success')
                    ->visible(fn (Campaign $record): bool => $record->status === 'active'),

                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\CouponsRelationManager::class,
            RelationManagers\WinnersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCampaigns::route('/'),
            // 'create' => Pages\CreateCampaign::route('/create'),
            'draw' => Pages\DrawCampaign::route('/{record}/draw'),
        ];
    }
}
