<?php

namespace App\Filament\Resources\WinnerResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class CampaignRelationManager extends RelationManager
{
    protected static string $relationship = 'campaign';

    protected static ?string $recordTitleAttribute = 'name';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),

                Forms\Components\Textarea::make('description')
                    ->maxLength(65535)
                    ->columnSpanFull(),

                Forms\Components\DatePicker::make('start_date')
                    ->required()
                    ->native(false),

                Forms\Components\DatePicker::make('end_date')
                    ->required()
                    ->native(false)
                    ->after('start_date'),

                Forms\Components\DatePicker::make('draw_date')
                    ->required()
                    ->native(false)
                    ->after('end_date'),

                Forms\Components\Select::make('status')
                    ->options([
                        'active' => 'Active',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ])
                    ->default('active')
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),

                Tables\Columns\TextColumn::make('start_date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('end_date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('draw_date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'completed' => 'info',
                        'cancelled' => 'danger',
                    }),

                Tables\Columns\TextColumn::make('coupons_count')
                    ->counts('coupons')
                    ->label('Coupons'),

                Tables\Columns\TextColumn::make('winners_count')
                    ->counts('winners')
                    ->label('Winners'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                // No header actions as this is a belongs-to relationship
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // No bulk actions needed
            ]);
    }
}
