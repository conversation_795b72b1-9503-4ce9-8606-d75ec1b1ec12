<?php

namespace App\Filament\Resources\WinnerResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class CouponRelationManager extends RelationManager
{
    protected static string $relationship = 'coupon';

    protected static ?string $recordTitleAttribute = 'code';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('code')
                    ->required()
                    ->maxLength(255)
                    ->disabled(),

                Forms\Components\Toggle::make('expired')
                    ->required(),

                Forms\Components\Select::make('transaction_id')
                    ->relationship('transaction', 'transaction_id')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->disabled(),

                Forms\Components\Select::make('campaign_id')
                    ->relationship('campaign', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->disabled(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('code')
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->copyable(),

                Tables\Columns\IconColumn::make('expired')
                    ->boolean()
                    ->label('Status')
                    ->trueIcon('heroicon-o-x-circle')
                    ->falseIcon('heroicon-o-check-circle')
                    ->trueColor('danger')
                    ->falseColor('success'),

                Tables\Columns\TextColumn::make('transaction.transaction_id')
                    ->numeric()
                    ->sortable()
                    ->label('Transaction ID'),

                Tables\Columns\TextColumn::make('transaction.name')
                    ->searchable()
                    ->label('Customer'),

                Tables\Columns\TextColumn::make('campaign.name')
                    ->searchable()
                    ->sortable()
                    ->label('Campaign'),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                // No header actions as this is a belongs-to relationship
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // No bulk actions needed
            ]);
    }
}
