<?php

namespace App\Filament\Resources\CouponResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class TransactionRelationManager extends RelationManager
{
    protected static string $relationship = 'transaction';

    protected static ?string $recordTitleAttribute = 'transaction_id';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->maxLength(255),

                Forms\Components\TextInput::make('email')
                    ->email()
                    ->maxLength(255),

                Forms\Components\TextInput::make('transaction_id')
                    ->required()
                    ->numeric()
                    ->disabled(),

                Forms\Components\DatePicker::make('transaction_date')
                    ->required(),

                Forms\Components\TextInput::make('transaction_sum')
                    ->required()
                    ->numeric()
                    ->label('Transaction Amount (Rp)'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('transaction_id')
            ->columns([
                Tables\Columns\TextColumn::make('transaction_id')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('name')
                    ->searchable(),

                Tables\Columns\TextColumn::make('email')
                    ->searchable(),

                Tables\Columns\TextColumn::make('transaction_date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('transaction_sum')
                    ->numeric()
                    ->money('IDR')
                    ->sortable()
                    ->label('Amount'),

                Tables\Columns\TextColumn::make('coupons_count')
                    ->counts('coupons')
                    ->label('Coupons'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                // No header actions as this is a belongs-to relationship
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // No bulk actions needed
            ]);
    }
}
