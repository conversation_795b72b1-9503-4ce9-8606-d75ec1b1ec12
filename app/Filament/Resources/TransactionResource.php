<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TransactionResource\Pages;
use App\Filament\Resources\TransactionResource\RelationManagers;
use App\Models\Transaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class TransactionResource extends Resource
{
    protected static ?string $model = Transaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'Transactions';

    protected static ?string $modelLabel = 'Transaction';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->maxLength(255)
                    ->disabled()
                    ->dehydrated(false),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->maxLength(255)
                    ->disabled()
                    ->dehydrated(false),
                Forms\Components\TextInput::make('tenant_id')
                    ->numeric()
                    ->disabled()
                    ->dehydrated(false)
                    ->label('Tenant ID'),
                Forms\Components\DateTimePicker::make('transaction_from')
                    ->disabled()
                    ->dehydrated(false)
                    ->label('Transaction From'),
                Forms\Components\DateTimePicker::make('transaction_to')
                    ->disabled()
                    ->dehydrated(false)
                    ->label('Transaction To'),
                Forms\Components\TextInput::make('transaction_sum')
                    ->numeric()
                    ->disabled()
                    ->dehydrated(false)
                    ->label('Transaction Amount (Rp)')
                    ->helperText('Total amount from aggregated payments'),
                Forms\Components\TextInput::make('transaction_status')
                    ->disabled()
                    ->dehydrated(false)
                    ->label('Status'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('tenant_id')
                    ->label('Tenant ID')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('transaction_from')
                    ->dateTime()
                    ->sortable()
                    ->label('From')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('transaction_to')
                    ->dateTime()
                    ->sortable()
                    ->label('To')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('transaction_sum')
                    ->numeric()
                    ->money('IDR')
                    ->sortable()
                    ->label('Amount'),
                Tables\Columns\TextColumn::make('transaction_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'success' => 'success',
                        default => 'gray',
                    })
                    ->label('Status'),
                Tables\Columns\TextColumn::make('coupons_count')
                    ->counts('coupons')
                    ->label('Coupons'),
            ])
            ->filters([
                Tables\Filters\Filter::make('has_coupons')
                    ->query(fn (Builder $query): Builder => $query->has('coupons'))
                    ->label('Has Coupons'),

                Tables\Filters\Filter::make('no_coupons')
                    ->query(fn (Builder $query): Builder => $query->doesntHave('coupons'))
                    ->label('No Coupons'),

                Tables\Filters\Filter::make('eligible_for_coupons')
                    ->query(fn (Builder $query): Builder => $query->where('transaction_sum', '>=', 300000))
                    ->label('Eligible for Coupons'),

                DateRangeFilter::make('transaction_date')
                    ->label('Transaction Date Range'),
            ])
            ->actions([
                Tables\Actions\Action::make('generate_coupons')
                    ->label('Generate Coupons')
                    ->icon('heroicon-o-ticket')
                    ->color('success')
                    ->requiresConfirmation()
                    ->form([
                        Forms\Components\Select::make('campaign_id')
                            ->label('Campaign')
                            ->options(\App\Models\Campaign::where('status', 'active')->pluck('name', 'id'))
                            ->required()
                            ->helperText('Select the campaign to generate coupons for'),
                    ])
                    ->action(function (Transaction $record, array $data): void {
                        $campaign = \App\Models\Campaign::findOrFail($data['campaign_id']);
                        $couponService = new \App\Services\CouponGenerationService;
                        $coupons = $couponService->generateForTransaction($record, $campaign);

                        if ($coupons->isEmpty()) {
                            \Filament\Notifications\Notification::make()
                                ->title('No coupons generated')
                                ->body('No coupons were generated. This could be because the transaction is not eligible, already has coupons for this campaign, or is outside the campaign date range.')
                                ->warning()
                                ->send();
                        } else {
                            \Filament\Notifications\Notification::make()
                                ->title('Coupons Generated')
                                ->body("Generated {$coupons->count()} coupons for this transaction.")
                                ->success()
                                ->send();
                        }
                    })
                    ->visible(fn (Transaction $record): bool => $record->transaction_sum >= 300000),

                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\CouponsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransactions::route('/'),
            // 'create' => Pages\CreateTransaction::route('/create'),
            // 'edit' => Pages\EditTransaction::route('/{record}/edit'),
        ];
    }
}
