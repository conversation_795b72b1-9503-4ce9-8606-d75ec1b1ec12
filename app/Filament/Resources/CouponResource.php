<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CouponResource\Pages;
use App\Filament\Resources\CouponResource\RelationManagers;
use App\Models\Coupon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CouponResource extends Resource
{
    protected static ?string $model = Coupon::class;

    protected static ?string $navigationIcon = 'heroicon-o-ticket';

    protected static ?string $navigationLabel = 'Coupons';

    protected static ?string $modelLabel = 'Coupon';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Coupon Information')
                    ->schema([
                        Forms\Components\TextInput::make('code')
                            ->required()
                            ->maxLength(255)
                            ->disabled(fn (?Coupon $record) => $record !== null)
                            ->helperText('Coupon code is automatically generated and cannot be changed'),

                        Forms\Components\Toggle::make('expired')
                            ->required()
                            ->label('Is Expired'),

                        Forms\Components\Select::make('transaction_id')
                            ->relationship('transaction', 'transaction_id')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->label('Transaction'),

                        Forms\Components\Select::make('campaign_id')
                            ->relationship('campaign', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->label('Campaign'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->copyable()
                    ->color(fn (Coupon $record): string => $record->expired ? 'gray' : 'primary'),

                // Tables\Columns\IconColumn::make('expired')
                //     ->boolean()
                //     ->label('Status')
                //     ->trueIcon('heroicon-o-x-circle')
                //     ->falseIcon('heroicon-o-check-circle')
                //     ->trueColor('danger')
                //     ->falseColor('success'),

                // Tables\Columns\TextColumn::make('transaction.transaction_id')
                //     ->sortable()
                //     ->label('Transaction ID'),

                Tables\Columns\TextColumn::make('transaction.name')
                    ->searchable()
                    ->label('Customer'),

                Tables\Columns\TextColumn::make('campaign.name')
                    ->searchable()
                    ->sortable()
                    ->label('Campaign'),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('expired')
                    ->placeholder('All Coupons')
                    ->trueLabel('Expired Coupons')
                    ->falseLabel('Active Coupons')
                    ->queries(
                        true: fn (Builder $query) => $query->where('expired', true),
                        false: fn (Builder $query) => $query->where('expired', false),
                        blank: fn (Builder $query) => $query,
                    ),

                Tables\Filters\SelectFilter::make('campaign_id')
                    ->relationship('campaign', 'name')
                    ->searchable()
                    ->preload()
                    ->label('Campaign'),
            ])
            ->actions([
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\TransactionRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCoupons::route('/'),
        ];
    }
}
