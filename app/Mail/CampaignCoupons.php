<?php

namespace App\Mail;

use App\Models\Campaign;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use stdClass;

class CampaignCoupons extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The user instance.
     *
     * @var stdClass
     */
    public $user;

    /**
     * The coupons for the user.
     *
     * @var array
     */
    public $coupons;

    /**
     * The campaign instance.
     *
     * @var Campaign
     */
    public $campaign;

    /**
     * Create a new message instance.
     */
    public function __construct(stdClass $user, array $coupons, Campaign $campaign)
    {
        $this->user = $user;
        $this->coupons = $coupons;
        $this->campaign = $campaign;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: '🥳 Kupon Undianmu Sudah Siap! Raih Kesempatan Menang Emas dari Viding!',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.campaign-coupons',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
