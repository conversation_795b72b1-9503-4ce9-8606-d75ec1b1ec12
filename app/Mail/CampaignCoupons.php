<?php

namespace App\Mail;

use App\Models\Campaign;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use stdClass;

class CampaignCoupons extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The user instance.
     *
     * @var stdClass
     */
    public $user;

    /**
     * The coupons for the user.
     *
     * @var array
     */
    public $coupons;

    /**
     * The campaign instance.
     *
     * @var Campaign
     */
    public $campaign;

    /**
     * Create a new message instance.
     */
    public function __construct(stdClass $user, array $coupons, Campaign $campaign)
    {
        $this->user = $user;
        $this->coupons = $coupons;
        $this->campaign = $campaign;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        // Use campaign's custom subject or default
        $subject = $this->campaign->email_subject;

        // Replace placeholders in subject
        $subject = $this->campaign->replacePlaceholders($subject, [
            'user_name' => $this->user->name,
            'coupon_count' => count($this->coupons),
        ]);

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.campaign-coupons',
            with: $this->getEmailData(),
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Get the email data with processed content.
     */
    public function getEmailData(): array
    {
        // Generate coupons HTML
        $couponsHtml = '';
        foreach ($this->coupons as $coupon) {
            $couponsHtml .= '<div style="background-color: #ffffff; border: 2px dashed #FFD700; border-radius: 8px; padding: 15px; margin-bottom: 10px; text-align: center;">';
            $couponsHtml .= '<div style="font-family: monospace; font-size: 18px; font-weight: bold; color: #0066cc; letter-spacing: 1px; padding: 8px 12px; background-color: #f0f8ff; border-radius: 4px; display: inline-block; margin: 5px 0;">';
            $couponsHtml .= htmlspecialchars($coupon->code);
            $couponsHtml .= '</div></div>';
        }

        // Prepare data for placeholder replacement
        $data = [
            'user_name' => $this->user->name,
            'coupon_count' => count($this->coupons),
            'coupons_html' => $couponsHtml,
            'transaction_date' => isset($this->user->transaction_date) ? $this->user->transaction_date->format('d M Y') : '',
        ];

        // Get processed email body
        $emailBody = $this->campaign->replacePlaceholders($this->campaign->email_body, $data);

        return [
            'user' => $this->user,
            'coupons' => $this->coupons,
            'campaign' => $this->campaign,
            'emailBody' => $emailBody,
            'couponsHtml' => $couponsHtml,
        ];
    }
}
