<?php

namespace App\Services;

use App\Helpers\UniqueCoupon;
use App\Models\Campaign;
use App\Models\Coupon;
use App\Models\Transaction;
use Illuminate\Support\Collection;

/**
 * Handles coupon generation and validation logic
 *
 * Responsible for:
 * - Creating coupons based on transaction amounts
 * - Ensuring proper coupon code generation
 * - Validating coupon eligibility
 */
class CouponGenerationService
{
    /**
     * Generate coupons for a transaction based on the transaction amount.
     * The number of coupons is determined by the campaign's coupon threshold.
     *
     * @param  Transaction  $transaction  The transaction to generate coupons for
     * @param  Campaign|\Illuminate\Database\Eloquent\Collection  $campaign  The campaign the coupons belong to
     * @return Collection Generated coupons
     */
    public function generateForTransaction(Transaction $transaction, Campaign|\Illuminate\Database\Eloquent\Collection $campaign): Collection
    {
        // Check if transaction period overlaps with campaign period
        $transactionFrom = $transaction->transaction_from;
        $transactionTo = $transaction->transaction_to;

        // Check if there's any overlap between transaction period and campaign period
        $hasOverlap = ($transactionFrom <= $campaign->end_date && $transactionTo >= $campaign->start_date);

        if (! $hasOverlap) {
            return collect([]);
        }

        // Check if coupons already exist for this transaction and campaign
        $existingCount = Coupon::where('transaction_id', $transaction->transaction_id)
            ->where('campaign_id', $campaign->id)
            ->count();

        if ($existingCount > 0) {
            return collect([]);
        }

        // Calculate number of coupons to generate based on campaign's coupon threshold
        $couponThreshold = $campaign->coupon_threshold ?? 300000; // Default fallback
        $couponCount = floor($transaction->transaction_sum / $couponThreshold);
        $coupons = collect([]);

        // Generate the coupons
        for ($i = 0; $i < $couponCount; $i++) {
            $coupon = $this->createCoupon($transaction, $campaign);
            $coupons->push($coupon);
        }

        return $coupons;
    }

    /**
     * Creates a single coupon with a unique code
     *
     * @return Coupon The generated coupon
     */
    protected function createCoupon(Transaction $transaction, Campaign $campaign): Coupon
    {
        $coupon = Coupon::create([
            'transaction_id' => $transaction->transaction_id,
            'campaign_id' => $campaign->id,
            'code' => $this->generateCouponCode(),
            'expired' => false,
        ]);

        return $coupon;
    }

    /**
     * Generates a unique coupon code
     *
     * @return string The generated coupon code
     */
    protected function generateCouponCode(): string
    {
        return UniqueCoupon::generate();
    }

    /**
     * Generate coupons for all eligible transactions in a campaign.
     *
     * @param  Campaign  $campaign  The campaign to generate coupons for
     * @return Collection Generated coupons
     */
    public function generateForCampaign(Campaign $campaign): Collection
    {
        // Get transactions that overlap with the campaign period
        $transactions = $campaign->eligibleTransactions()->get();
        $generatedCoupons = collect([]);

        foreach ($transactions as $transaction) {
            $coupons = $this->generateForTransaction($transaction, $campaign);
            $generatedCoupons = $generatedCoupons->merge($coupons);
        }

        return $generatedCoupons;
    }

    /**
     * Get the count of coupons that would be generated for a transaction.
     *
     * @param  Transaction  $transaction  The transaction to calculate coupons for
     * @param  Campaign  $campaign  The campaign to use for coupon threshold
     * @return int Number of coupons that would be generated
     */
    public function getCouponCountForTransaction(Transaction $transaction, Campaign $campaign): int
    {
        $couponThreshold = $campaign->coupon_threshold ?? 300000; // Default fallback

        return floor($transaction->transaction_sum / $couponThreshold);
    }
}
