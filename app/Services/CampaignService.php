<?php

namespace App\Services;

use App\Models\Campaign;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class CampaignService
{
    /**
     * Get all active campaigns.
     */
    public function getActiveCampaigns(): Collection
    {
        return Campaign::where('status', 'active')
            ->where('end_date', '>=', Carbon::now())
            ->orderBy('start_date')
            ->get();
    }

    /**
     * Get campaigns that are ready for drawing (draw date is today or in the past).
     */
    public function getCampaignsReadyForDraw(): Collection
    {
        return Campaign::where('status', 'active')
            ->where('draw_date', '<=', Carbon::now())
            ->orderBy('draw_date')
            ->get();
    }

    /**
     * Get all eligible transactions for a campaign based on date range.
     */
    public function getEligibleTransactions(Campaign $campaign): Collection
    {
        return Transaction::whereBetween('transaction_date', [$campaign->start_date, $campaign->end_date])
            ->orderBy('transaction_date', 'desc')
            ->get();
    }

    /**
     * Complete a campaign after drawing.
     */
    public function completeCampaign(Campaign $campaign): bool
    {
        $campaign->status = 'completed';

        return $campaign->save();
    }

    /**
     * Cancel a campaign.
     */
    public function cancelCampaign(Campaign $campaign): bool
    {
        $campaign->status = 'cancelled';

        return $campaign->save();
    }

    /**
     * Get campaign statistics.
     */
    public function getCampaignStatistics(Campaign $campaign): array
    {
        $couponService = new CouponGenerationService;
        $drawService = new DrawService;

        $transactionCount = $this->getEligibleTransactions($campaign)->count();
        $couponCount = $campaign->coupons()->count();
        $activeCouponCount = $campaign->coupons()->where('expired', false)->count();
        $winnerCount = $campaign->winners()->count();

        return [
            'transaction_count' => $transactionCount,
            'coupon_count' => $couponCount,
            'active_coupon_count' => $activeCouponCount,
            'winner_count' => $winnerCount,
        ];
    }
}
