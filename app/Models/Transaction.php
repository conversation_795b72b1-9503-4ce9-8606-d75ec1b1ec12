<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     * This is now a view that aggregates data from the legacy database.
     *
     * @var string
     */
    protected $table = 'transactions';

    /**
     * The primary key for the model.
     * Since this is a view, we use 'id' as the primary key.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'tenant_id',
        'email',
        'name',
        'transaction_from',
        'transaction_to',
        'transaction_sum',
        'transaction_status',
        'transaction_id',
        'transaction_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'transaction_from' => 'datetime',
        'transaction_to' => 'datetime',
        'transaction_date' => 'datetime',
        'transaction_sum' => 'integer',
        'tenant_id' => 'integer',
    ];

    /**
     * Indicates if the model should be timestamped.
     * Since this is a view, we don't want <PERSON><PERSON> to try to update timestamps.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Get the coupons for this transaction.
     */
    public function coupons()
    {
        return $this->hasMany(Coupon::class, 'transaction_id', 'transaction_id');
    }
}
