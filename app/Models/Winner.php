<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Winner extends Model
{
    use HasFactory;

    protected $fillable = [
        'campaign_id',
        'coupon_id',
        'name',
        'email',
        'won_at',
    ];

    protected $casts = [
        'won_at' => 'datetime',
    ];

    /**
     * Get the campaign that this winner belongs to.
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Get the coupon that was drawn for this winner.
     */
    public function coupon()
    {
        return $this->belongsTo(Coupon::class);
    }
}
