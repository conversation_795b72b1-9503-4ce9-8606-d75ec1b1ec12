<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coupon extends Model
{
    /** @use HasFactory<\Database\Factories\CouponFactory> */
    use HasFactory;

    protected $fillable = [
        'transaction_id',
        'campaign_id',
        'code',
        'expired',
    ];

    protected $casts = [
        'expired' => 'boolean',
    ];

    /**
     * Get the transaction that this coupon belongs to.
     */
    public function transaction()
    {
        return $this->belongsTo(Transaction::class, 'transaction_id', 'transaction_id');
    }

    /**
     * Get the campaign that this coupon belongs to.
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Get the winner record if this coupon was drawn.
     */
    public function winner()
    {
        return $this->hasOne(Winner::class);
    }

    /**
     * Scope a query to only include active (non-expired) coupons.
     */
    public function scopeActive($query)
    {
        return $query->where('expired', false);
    }
}
