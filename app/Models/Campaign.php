<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Campaign extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'email_subject',
        'email_body',
        'start_date',
        'end_date',
        'draw_date',
        'status',
        'running',
        'max_winners',
        'coupon_threshold',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'draw_date' => 'date',
        'running' => 'boolean',
        'max_winners' => 'integer',
        'coupon_threshold' => 'integer',
    ];

    /**
     * Get the coupons for this campaign.
     */
    public function coupons()
    {
        return $this->hasMany(Coupon::class);
    }

    /**
     * Get the winners for this campaign.
     */
    public function winners()
    {
        return $this->hasMany(Winner::class);
    }

    /**
     * Get eligible transactions for this campaign based on date range.
     * Now uses transaction_from and transaction_to for date range checking.
     */
    public function eligibleTransactions()
    {
        return Transaction::where(function ($query) {
            $query->whereBetween('transaction_from', [$this->start_date, $this->end_date])
                ->orWhereBetween('transaction_to', [$this->start_date, $this->end_date])
                ->orWhere(function ($subQuery) {
                    $subQuery->where('transaction_from', '<=', $this->start_date)
                        ->where('transaction_to', '>=', $this->end_date);
                });
        });
    }

    /**
     * Get the count of eligible transactions for this campaign.
     *
     * @return int The number of transactions eligible for this campaign based on date range
     */
    public function getEligibleTransactionCountAttribute()
    {
        return $this->eligibleTransactions()->count();
    }

    /**
     * Replace placeholders in email content with actual values.
     */
    public function replacePlaceholders(string $content, array $data = []): string
    {
        $placeholders = [
            '[NAME]' => $data['user_name'] ?? '',
            '[COUPON_COUNT]' => $data['coupon_count'] ?? 0,
            '[COUPONS]' => $data['coupons_html'] ?? '',
            '[TRANSACTION_DATE]' => $data['transaction_date'] ?? '',
            '[START_DATE]' => $this->start_date->format('d M Y'),
            '[END_DATE]' => $this->end_date->format('d M Y'),
            '[DRAW_DATE]' => $this->draw_date->format('d M Y'),
            '[CAMPAIGN_NAME]' => $this->name,
            '[CAMPAIGN_DESCRIPTION]' => $this->description,
        ];

        return str_replace(array_keys($placeholders), array_values($placeholders), $content);
    }

    /**
     * Get the default email subject if none is set.
     */
    public function getEmailSubjectAttribute($value): string
    {
        return $value ?: '🥳 Kupon Undianmu Sudah Siap! Raih Kesempatan Menang Emas dari Viding!';
    }

    /**
     * Get the default email body if none is set.
     */
    public function getEmailBodyAttribute($value): string
    {
        if ($value) {
            return $value;
        }

        // Default email body template
        return 'Halo [NAME],

Terima kasih telah menggunakan Viding.co! 🎉
Sebagai bentuk apresiasi atas transaksi yang telah kamu lakukan, kami memberikan [COUPON_COUNT] Kupon Undian Emas khusus untukmu.

📩 Detail Kupon:
[COUPONS]
Tanggal Transaksi: [TRANSACTION_DATE]

🎁 Hadiah Utama:
🏆 Emas eksklusif untuk pengguna Viding!

🤞 Semakin banyak transaksi, semakin besar peluang kamu memenangkan hadiah menarik dari kami. Jangan lewatkan kesempatan spesial ini!

Program ini berlaku untuk pemesanan di periode [START_DATE] - [END_DATE]

📆 Pengumuman pemenang akan kami umumkan di Live Instagram Viding pada [DRAW_DATE].
*TnC applied

Jika kamu memiliki pertanyaan, jangan ragu untuk menghubungi tim support kami.

Salam hangat,
Tim Viding.co
https://viding.co';
    }
}
