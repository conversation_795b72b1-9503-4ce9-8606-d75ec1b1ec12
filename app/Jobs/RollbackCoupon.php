<?php

namespace App\Jobs;

use App\Models\Campaign;
use App\Models\Coupon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class RollbackCoupon implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(private readonly Campaign $campaign) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->campaign->running) {
            Log::info("Campaign {$this->campaign->id} is running. Skipping coupon rollback.");

            return;
        }

        Log::info("Starting coupon rollback for campaign {$this->campaign->id}");

        Coupon::where('campaign_id', $this->campaign->id)->delete();

        Log::info("Coupon rollback completed for campaign {$this->campaign->id}");
    }
}
