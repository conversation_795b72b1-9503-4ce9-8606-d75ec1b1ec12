<?php

namespace App\Jobs;

use App\Models\Campaign;
use App\Models\Transaction;
use App\Services\CouponGenerationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendCampaignCouponsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The campaign instance.
     *
     * @var \App\Models\Campaign
     */
    protected $campaign;

    /**
     * Create a new job instance.
     */
    public function __construct(Campaign $campaign)
    {
        $this->campaign = $campaign;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Only proceed if the campaign is running
        if (! $this->campaign->running) {
            Log::info("Campaign {$this->campaign->id} is not running. Skipping coupon generation.");

            return;
        }

        Log::info("Starting coupon generation for campaign {$this->campaign->id}");

        // Generate coupons for eligible transactions
        $couponService = new CouponGenerationService;
        $generatedCoupons = $couponService->generateForCampaign($this->campaign);

        if ($generatedCoupons->isEmpty()) {
            Log::info("No new coupons generated for campaign {$this->campaign->id}");

            return;
        }

        Log::info("Generated {$generatedCoupons->count()} coupons for campaign {$this->campaign->id}");

        // Group coupons by user (transaction owner)
        $couponsByUser = $this->groupCouponsByUser($generatedCoupons);

        // Send emails to users with their coupons
        foreach ($couponsByUser as $userId => $userCoupons) {
            $this->sendCouponEmail($userId, $userCoupons);
        }

        Log::info("Completed sending coupon emails for campaign {$this->campaign->id}");
    }

    /**
     * Group coupons by user ID.
     *
     * @param  \Illuminate\Support\Collection  $coupons
     * @return array
     */
    protected function groupCouponsByUser($coupons)
    {
        $couponsByUser = [];

        foreach ($coupons as $coupon) {
            $transaction = Transaction::find($coupon->transaction_id);
            if (! $transaction) {
                continue;
            }

            $userId = $transaction->user_id;
            if (! isset($couponsByUser[$userId])) {
                $couponsByUser[$userId] = [];
            }

            $couponsByUser[$userId][] = $coupon;
        }

        return $couponsByUser;
    }

    /**
     * Send an email to a user with their coupons.
     *
     * @param  int  $userId
     * @param  array  $coupons
     * @return void
     */
    protected function sendCouponEmail($userId, $coupons)
    {
        // Get the first transaction to extract email information
        // We can use any transaction since they all belong to the same user
        $firstCoupon = reset($coupons);
        $transaction = Transaction::where('transaction_id', $firstCoupon->transaction_id)->first();

        if (! $transaction || ! $transaction->email) {
            Log::warning("Unable to send coupon email: Transaction for coupon {$firstCoupon->id} has no email");

            return;
        }

        // Create a user object with necessary properties for the email template
        $user = new \stdClass;
        $user->name = $transaction->name;
        $user->email = $transaction->email;
        $user->transaction_date = $transaction->transaction_date;

        try {
            Mail::to($transaction->email)->send(new \App\Mail\CampaignCoupons($user, $coupons, $this->campaign));
            Log::info("Sent coupon email to {$transaction->email} with ".count($coupons).' coupons');
        } catch (\Exception $e) {
            Log::error("Failed to send coupon email to {$transaction->email}: ".$e->getMessage());
        }
    }
}
