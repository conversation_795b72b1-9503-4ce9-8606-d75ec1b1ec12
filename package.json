{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "dependencies": {"axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "vite": "^6.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "lightningcss-linux-x64-gnu": "^1.29.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "laravel-echo": "^2.1.4", "postcss": "^8.5.3", "postcss-nesting": "^13.0.1", "pusher-js": "^8.4.0", "tailwindcss": "^3.4.17"}}