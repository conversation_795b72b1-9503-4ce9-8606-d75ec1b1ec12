/* Pagination component styles */

.filament-tables-pagination {
    @apply flex items-center justify-between px-4 py-2;
}

.filament-tables-pagination-records-per-page-selector {
    @apply flex items-center gap-2 text-sm font-medium;
}

.filament-tables-pagination-records-per-page-selector select {
    @apply h-8 rounded-lg border-gray-300 pr-8 text-sm focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:focus:border-primary-500;
}

.filament-tables-pagination-container {
    @apply flex items-center gap-2;
}

/* Pagination navigation */
.filament-tables-pagination-container nav {
    @apply flex items-center gap-1;
}

/* All pagination links and spans */
.filament-tables-pagination-container nav a,
.filament-tables-pagination-container nav span {
    @apply inline-flex h-8 min-w-[2rem] items-center justify-center rounded-lg px-2 text-sm font-medium;
}

/* Regular pagination links */
.filament-tables-pagination-container nav a:not(.cursor-default) {
    @apply text-gray-700 outline-none transition hover:bg-gray-50 focus:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-800 dark:focus:bg-gray-800;
}

/* Disabled pagination items */
.filament-tables-pagination-container nav span.cursor-default {
    @apply text-gray-400 dark:text-gray-500;
}

/* Active pagination link */
.filament-tables-pagination-container nav a.cursor-default {
    @apply bg-primary-50 text-primary-600 dark:bg-primary-900/20 dark:text-primary-400;
}

/* Pagination text */
.filament-tables-pagination-container p {
    @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}
