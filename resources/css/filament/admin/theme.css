@import '../../../../vendor/filament/filament/resources/css/theme.css';

@config 'tailwind.config.js';

/* Import component styles */
/* @import './components/pagination.css'; */


/* .fi-ta-table > tbody {
    @apply divide-none
}


.fi-ta-table > tbody {
    @apply divide-none
}

.fi-ta-text, .fi-pagination-item-label, .fi-pagination-item-button, .fi-pagination-item {
    @apply py-1
}

.fi-pagination-item-label > span {
    @apply text-xs
}

.fi-ta-header-cell {
    @apply py-2
}

.fi-ta-table > .whitespace-nowrap {
    @apply py-1
}

.fi-ta-cell > div:has(.flex) {
    @apply py-1
}

.fi-input, .fi-select-input, .fi-fo-field-wrp-label > span {
    @apply text-xs;
}

.fi-fo-date-time-picker-display-text-input, .fi-fo-field-wrp-error-message {
    @apply text-xs;
}

.fi-section-content {
    @apply p-2;
}

.fi-fo-component-ctn {
    @apply gap-2;
} */
