<x-filament-panels::page @start-draw-sound.window="startDrawSequence()"
    @draw-loading-started.window="showLoadingSpinner()" @draw-error-occurred.window="hideLoadingSpinner()">
    <div>
        <!-- CSS to ensure winners section is always visible -->
        <style>
            .always-visible {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
        </style>
        
        <!-- Immediate script to force winners section visibility -->
        <script>
            // Force winners section to be visible immediately
            document.addEventListener('DOMContentLoaded', function () {
                // Ensure winners section is visible immediately
                setTimeout(function () {
                    const winnersSection = document.getElementById('winners-section');
                    if (winnersSection) {
                        winnersSection.style.display = 'block';
                        winnersSection.style.visibility = 'visible';
                        winnersSection.classList.add('always-visible');
                        console.log('IMMEDIATE: Forcing winners section visibility');
                    }
                }, 100); // Small delay to ensure DOM is ready
                
// Observe DOM mutations once instead of polling
const observer = new MutationObserver(() => {
    if (winnersSection && (winnersSection.style.display !== 'block' || winnersSection.style.visibility !== 'visible')) {
        winnersSection.style.display = 'block';
        winnersSection.style.visibility = 'visible';
        winnersSection.classList.add('always-visible');
        console.debug('MUTATION: Reinforcing winners section visibility');
    }
});
observer.observe(document.body, { attributes: true, childList: true, subtree: true });
            });
        </script>
        <!-- We'll create the loading overlay dynamically with JavaScript -->

        <!-- Add confetti JS library and setup -->
        <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>

        <!-- Add audio element for celebration sound -->
        <audio id="celebrationSound" preload="auto">
            <source src="{{ asset('sound/soundeffect.mp3') }}" type="audio/mpeg">
            Your browser does not support the audio element.
        </audio>
        <script>
            // Variable to hold the winner display element
            let winnerDisplayElement = null;

            // Create the loading spinner dynamically
            let loadingOverlay = null;

            // Variable to hold the winner modal
            let winnerModal = null;

            // Variable to store the focused element before modal opens
            let previouslyFocusedElement = null;

            // Configurable timing parameters (in milliseconds)
            const CONFIG = {
                // Sound effect duration before winner reveal
                soundDuration: 6000,
                // Fallback timing if sound fails to play
                fallbackDuration: 3000,
                // Delay before showing notification after winner reveal
                notificationDelay: 1500,
                // Duration of notification display
                notificationDuration: 8000,
                // Main confetti animation duration
                confettiDuration: 8000,
                // Whether to use reduced motion (auto-detects from system settings)
                reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches
            };

            // Performance monitoring
            const PERF = {
                drawStart: 0,
                loadingShown: 0,
                soundStarted: 0,
                winnerRevealed: 0,
                sequenceComplete: 0
            };

            function createLoadingOverlay() {
                // Create the overlay element if it doesn't exist
                if (!loadingOverlay) {
                    loadingOverlay = document.createElement('div');
                    loadingOverlay.id = 'loadingOverlay';
                    loadingOverlay.style.position = 'fixed';
                    loadingOverlay.style.top = '0';
                    loadingOverlay.style.left = '0';
                    loadingOverlay.style.width = '100%';
                    loadingOverlay.style.height = '100%';
                    loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                    loadingOverlay.style.backdropFilter = 'blur(10px)';
                    loadingOverlay.style.zIndex = '9999';
                    loadingOverlay.style.display = 'none';

                    // Create the content container
                    const contentContainer = document.createElement('div');
                    contentContainer.style.position = 'absolute';
                    contentContainer.style.top = '50%';
                    contentContainer.style.left = '50%';
                    contentContainer.style.transform = 'translate(-50%, -50%)';
                    contentContainer.style.textAlign = 'center';

                    // Create the spinner
                    const spinner = document.createElement('div');
                    spinner.style.width = '64px';
                    spinner.style.height = '64px';
                    spinner.style.margin = '0 auto';
                    spinner.style.border = '4px solid #e9088A';
                    spinner.style.borderRadius = '50%';
                    spinner.style.borderRightColor = 'transparent';
                    spinner.style.animation = 'spin 1s linear infinite';

                    // Create the text elements
                    const title = document.createElement('div');
                    title.style.marginTop = '16px';
                    title.style.fontSize = '24px';
                    title.style.fontWeight = 'bold';
                    title.style.color = 'white';
                    title.textContent = 'Drawing winner...';

                    const subtitle = document.createElement('div');
                    subtitle.style.marginTop = '8px';
                    subtitle.style.color = 'white';
                    subtitle.textContent = 'Get ready for the big reveal!';

                    // Add the elements to the DOM
                    contentContainer.appendChild(spinner);
                    contentContainer.appendChild(title);
                    contentContainer.appendChild(subtitle);
                    loadingOverlay.appendChild(contentContainer);
                    document.body.appendChild(loadingOverlay);

                    // Add the animation style
                    const style = document.createElement('style');
                    style.textContent = '@keyframes spin { to { transform: rotate(360deg); } }';
                    document.head.appendChild(style);
                }
            }

            // Function to show the loading spinner
            function showLoadingSpinner() {
                console.log('Showing loading spinner');
                createLoadingOverlay();
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'block';
                    console.log('Loading spinner displayed');
                    PERF.loadingShown = performance.now();
                    console.log('Loading shown time: ' + (PERF.loadingShown - PERF.drawStart) + 'ms');
                }
            }

            // Function to hide the loading spinner
            function hideLoadingSpinner() {
                console.log('Hiding loading spinner');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                    console.log('Loading spinner hidden');
                }
            }

            // Function to start the draw sequence with sound first, then reveal winner
            function startDrawSequence() {
                // Start performance tracking
                PERF.drawStart = performance.now();

                // Show loading spinner (if not already shown)
                showLoadingSpinner();

                // Hide any existing winner first (in case this is a new draw)
                hideWinner();

                // We no longer hide the winners section during the draw sequence
                // This ensures the recent winners are always visible
                const winnersSection = document.getElementById('winners-section');
                // Make sure it's visible (in case it was hidden previously)
                if (winnersSection) {
                    winnersSection.style.display = 'block';
                }

                // Play celebration sound
                const celebrationSound = document.getElementById('celebrationSound');
                if (celebrationSound) {
                    // Reset the audio to the beginning
                    celebrationSound.currentTime = 0;
                    // Set volume to a reasonable level
                    celebrationSound.volume = 1;
                    // Play the sound
                    const playPromise = celebrationSound.play();
                    PERF.soundStarted = performance.now();

                    // Handle potential play() promise rejection (browser policy may require user interaction)
                    if (playPromise !== undefined) {
                        playPromise.catch(error => {
                            console.log('Auto-play was prevented. Sound will only play after user interaction.');
                            console.log('Using fallback timing: ' + CONFIG.fallbackDuration + 'ms');
                            // If sound fails, still show the winner after the fallback delay
                            // Using a longer fallback to maintain suspense even without sound
                            setTimeout(revealWinnerWithConfetti, CONFIG.fallbackDuration);
                        });
                    }

                    // After sound buildup, reveal the winner with confetti
                    setTimeout(revealWinnerWithConfetti, CONFIG.soundDuration);
                } else {
                    // If sound element not found, still show the winner with fallback timing
                    console.warn('Celebration sound element not found, using fallback timing');
                    setTimeout(revealWinnerWithConfetti, CONFIG.fallbackDuration);
                }

                // Log performance data
                console.log('Draw sequence started at: ' + new Date().toISOString());
            }

            // Function to hide the winner display (compatibility function)
            function hideWinner() {
                // This function now just hides the main winner section
                // It's kept for compatibility with existing code that calls it
                const winnerDisplay = document.querySelector('.main-winner');
                if (winnerDisplay) {
                    winnerDisplay.style.display = 'none';
                    winnerDisplayElement = winnerDisplay;
                }

                // Also hide any open modal
                hideWinnerModal();

                // IMPORTANT: Always make sure the recent winners section is visible
                const winnersSection = document.getElementById('winners-section');
                if (winnersSection) {
                    winnersSection.style.display = 'block';
                    console.log('Ensuring winners section is visible');
                }
            }

            // Function to create the winner modal if it doesn't exist
            function createWinnerModal() {
                if (!winnerModal) {
                    // Create modal backdrop with blur effect
                    winnerModal = document.createElement('div');
                    winnerModal.id = 'winnerModal';
                    winnerModal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70 backdrop-blur-md';
                    winnerModal.style.display = 'none';
                    winnerModal.setAttribute('role', 'dialog');
                    winnerModal.setAttribute('aria-modal', 'true');
                    winnerModal.setAttribute('aria-labelledby', 'modal-title');
                    
                    // Create sentinel elements for focus trapping
                    const startSentinel = document.createElement('div');
                    startSentinel.setAttribute('tabindex', '0');
                    startSentinel.setAttribute('aria-hidden', 'true');
                    
                    const endSentinel = document.createElement('div');
                    endSentinel.setAttribute('tabindex', '0');
                    endSentinel.setAttribute('aria-hidden', 'true');
                    
                    // Add click handler to backdrop for closing when clicking outside the modal
                    winnerModal.addEventListener('click', function(e) {
                        // Only close if clicking directly on the backdrop (not on modal content)
                        if (e.target === winnerModal) {
                            hideWinnerModal();
                        }
                    });

                    // Add keyboard event listener for Escape key
                    winnerModal.addEventListener('keydown', function(e) {
                        if (e.key === 'Escape') {
                            hideWinnerModal();
                        }
                    });

                    // Handle focus trapping
                    startSentinel.addEventListener('focus', function() {
                        // Focus the last focusable element when tabbing backwards from start
                        const focusableElements = getFocusableElements();
                        if (focusableElements.length > 0) {
                            focusableElements[focusableElements.length - 1].focus();
                        }
                    });

                    endSentinel.addEventListener('focus', function() {
                        // Focus the first focusable element when tabbing forwards from end
                        const focusableElements = getFocusableElements();
                        if (focusableElements.length > 0) {
                            focusableElements[0].focus();
                        }
                    });

                    // Create modal content container with game show style
                    const modalContent = document.createElement('div');
                    modalContent.className = 'bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl shadow-2xl transform transition-all max-w-lg w-full mx-auto overflow-hidden border-4 border-primary-500 dark:border-primary-400';
                    modalContent.style.maxWidth = '550px';
                    
                    // Add shining effect container
                    const shineContainer = document.createElement('div');
                    shineContainer.className = 'absolute inset-0 overflow-hidden rounded-2xl pointer-events-none';
                    shineContainer.innerHTML = '<div class="shine-effect"></div>';
                    
                    // Create a simpler close button with inline styles
                    const closeButton = document.createElement('button');
                    closeButton.id = 'winnerModalCloseButton';
                    closeButton.setAttribute('type', 'button');
                    closeButton.setAttribute('aria-label', 'Close');
                    closeButton.style.position = 'absolute';
                    closeButton.style.top = '10px';
                    closeButton.style.right = '10px';
                    closeButton.style.zIndex = '60';
                    closeButton.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                    closeButton.style.color = 'white';
                    closeButton.style.border = 'none';
                    closeButton.style.borderRadius = '50%';
                    closeButton.style.width = '32px';
                    closeButton.style.height = '32px';
                    closeButton.style.cursor = 'pointer';
                    closeButton.style.display = 'flex';
                    closeButton.style.alignItems = 'center';
                    closeButton.style.justifyContent = 'center';
                    closeButton.style.fontSize = '20px';
                    closeButton.style.fontWeight = 'bold';
                    closeButton.innerHTML = '×'; // Simple X character
                    
                    // Direct onclick function
                    closeButton.onclick = function() {
                        console.log('Close button clicked directly');
                        winnerModal.style.display = 'none';
                    };

                    // Create content placeholder with game show styling
                    const contentPlaceholder = document.createElement('div');
                    contentPlaceholder.id = 'winnerModalContent';
                    contentPlaceholder.className = 'p-8 relative z-10';
                    
                    // Add decorative elements
                    const topDecoration = document.createElement('div');
                    topDecoration.className = 'absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-primary-500 via-yellow-400 to-primary-500';
                    
                    const bottomDecoration = document.createElement('div');
                    bottomDecoration.className = 'absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-primary-500 via-yellow-400 to-primary-500';

                    // Assemble modal
                    modalContent.appendChild(shineContainer);
                    modalContent.appendChild(topDecoration);
                    modalContent.appendChild(bottomDecoration);
                    modalContent.appendChild(closeButton);
                    modalContent.appendChild(contentPlaceholder);
                    winnerModal.appendChild(modalContent);
                    document.body.appendChild(winnerModal);
                    
                    // Add the shine effect animation style
                    const style = document.createElement('style');
                    style.textContent = `
                        .shine-effect {
                            position: absolute;
                            top: -100%;
                            left: -100%;
                            right: -100%;
                            bottom: -100%;
                            background: linear-gradient(45deg, transparent 45%, rgba(255, 255, 255, 0.2) 50%, transparent 55%);
                            animation: shine 3s infinite;
                        }
                        @keyframes shine {
                            0% { transform: translateY(100%) translateX(100%); }
                            100% { transform: translateY(-100%) translateX(-100%); }
                        }
                    `;
                    document.head.appendChild(style);
                }
            }

            // Function to get all focusable elements within the modal
            function getFocusableElements() {
                if (!winnerModal) return [];
                const focusableSelectors = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
                return Array.from(winnerModal.querySelectorAll(focusableSelectors))
                    .filter(el => !el.hasAttribute('disabled') && !el.getAttribute('aria-hidden'));
            }

            // Function to show the winner modal
            function showWinnerModal() {
                createWinnerModal();

                // Store the currently focused element
                previouslyFocusedElement = document.activeElement;

                // Get winner content from the page and clone it into the modal
                const winnerContent = document.querySelector('.main-winner .mx-auto');
                if (winnerContent && winnerModal) {
                    const modalContent = document.getElementById('winnerModalContent');
                    modalContent.innerHTML = '';

                    // Add dramatic game show header with spotlight effect
                    const header = document.createElement('div');
                    header.className = 'text-center mb-6 relative';
                    header.id = 'modal-title'; // Add ID for aria-labelledby
                    
                    // Create spotlight container
                    const spotlightContainer = document.createElement('div');
                    spotlightContainer.className = 'absolute -top-20 left-1/2 transform -translate-x-1/2 pointer-events-none';
                    spotlightContainer.innerHTML = `
                        <div class="spotlight-beam"></div>
                        <div class="spotlight-beam" style="animation-delay: 0.5s;"></div>
                    `;
                    
                    // Create trophy icon
                    const trophyIcon = document.createElement('div');
                    trophyIcon.className = 'text-5xl mb-2 animate-bounce';
                    trophyIcon.innerHTML = '🏆';
                    
                    // Create title with animated text
                    const title = document.createElement('div');
                    title.className = 'text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-primary-400 to-yellow-400 animate-pulse';
                    title.innerHTML = 'WINNER!';
                    
                    // Create subtitle
                    const subtitle = document.createElement('div');
                    subtitle.className = 'text-xl font-bold text-white mt-1';
                    subtitle.innerHTML = 'Congratulations!';
                    
                    // Assemble header
                    header.appendChild(spotlightContainer);
                    header.appendChild(trophyIcon);
                    header.appendChild(title);
                    header.appendChild(subtitle);
                    modalContent.appendChild(header);

                    // Clone and enhance the winner content
                    const contentClone = winnerContent.cloneNode(true);
                    
                    // Apply game show styling to the content
                    contentClone.classList.add('transform', 'scale-105');
                    contentClone.style.transition = 'transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                    
                    // Find and enhance the winner box
                    const winnerBox = contentClone.querySelector('.bg-success-50');
                    if (winnerBox) {
                        winnerBox.className = 'mx-auto max-w-md rounded-lg bg-gradient-to-br from-success-900 to-success-700 p-6 shadow-lg border-2 border-success-400 dark:border-success-300';
                        
                        // Update text colors for better contrast
                        const nameElement = winnerBox.querySelector('.text-success-800');
                        if (nameElement) {
                            nameElement.className = 'mt-4 text-2xl font-bold text-white';
                        }
                        
                        const emailElement = winnerBox.querySelector('.text-success-600');
                        if (emailElement) {
                            emailElement.className = 'mt-2 text-sm text-success-200';
                        }
                        
                        const codeElement = winnerBox.querySelectorAll('.text-success-800')[1];
                        if (codeElement) {
                            codeElement.className = 'mt-4 text-2xl font-bold text-white bg-success-600 px-4 py-2 rounded-lg inline-block';
                        }
                        
                        const timeElement = winnerBox.querySelectorAll('.text-success-600')[1];
                        if (timeElement) {
                            timeElement.className = 'mt-2 text-sm text-success-200';
                        }
                    }
                    
                    modalContent.appendChild(contentClone);

                    // Show the modal with a dramatic zoom-in effect
                    winnerModal.style.display = 'flex';
                    winnerModal.style.opacity = '0';
                    winnerModal.style.transition = 'opacity 0.5s cubic-bezier(0.19, 1, 0.22, 1)';
                    
                    // Get the modal content for animation
                    const modalContentElement = winnerModal.querySelector('.bg-gradient-to-br');
                    console.log('Show modal content element:', modalContentElement);
                    
                    if (modalContentElement) {
                        modalContentElement.style.transform = 'scale(0.8)';
                        modalContentElement.style.transition = 'transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                    }
                    
                    setTimeout(() => {
                        winnerModal.style.opacity = '1';
                        winnerModal.style.transition = 'opacity 0.5s cubic-bezier(0.19, 1, 0.22, 1)';
                        if (modalContentElement) {
                            modalContentElement.style.transform = 'scale(1)';
                            modalContentElement.style.transition = 'transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                        }
                    }, 10);
                    
                    // Add spotlight effect style
                    const style = document.createElement('style');
                    style.textContent = `
                        .spotlight-beam {
                            width: 50px;
                            height: 200px;
                            background: linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0));
                            transform-origin: center top;
                            animation: rotate-spotlight 4s infinite ease-in-out;
                            opacity: 0.6;
                            border-radius: 50%;
                            filter: blur(5px);
                        }
                        @keyframes rotate-spotlight {
                            0%, 100% { transform: rotate(-30deg); }
                            50% { transform: rotate(30deg); }
                        }
                    `;
                    document.head.appendChild(style);
                }
            }

            // Function to hide the winner modal
            function hideWinnerModal() {
                console.log('hideWinnerModal called');
                
                if (!winnerModal) {
                    console.error('Winner modal not found');
                    return;
                }
                
                try {
                    // Get the modal content for zoom-out effect
                    const modalContent = winnerModal.querySelector('.bg-gradient-to-br');
                    console.log('Modal content element:', modalContent);
                    
                    // Apply zoom-out effect
                    if (modalContent) {
                        modalContent.style.transform = 'scale(0.8)';
                    } else {
                        console.warn('Modal content element not found for animation');
                    }
                    
                    // Fade out with transition
                    winnerModal.style.opacity = '0';
                    winnerModal.style.transition = 'opacity 0.3s ease-out';
                    
                    // Hide after animation completes and restore focus
                    setTimeout(() => {
                        console.log('Hiding winner modal after timeout');
                        winnerModal.style.display = 'none';
                        
                        // Reset transform for next time
                        if (modalContent) {
                            modalContent.style.transform = 'scale(1)';
                        }

                        // Restore focus to the previously focused element
                        if (previouslyFocusedElement && typeof previouslyFocusedElement.focus === 'function') {
                            previouslyFocusedElement.focus();
                        }
                    }, 300);
                } catch (error) {
                    console.error('Error in hideWinnerModal:', error);
                    // Fallback: just hide the modal immediately and restore focus
                    winnerModal.style.display = 'none';
                    if (previouslyFocusedElement && typeof previouslyFocusedElement.focus === 'function') {
                        previouslyFocusedElement.focus();
                    }
                }
            }

            // Function to reveal the winner with confetti
            function revealWinnerWithConfetti() {
                // Record performance timing
                PERF.winnerRevealed = performance.now();
                console.log('Winner reveal time: ' + (PERF.winnerRevealed - PERF.drawStart) + 'ms');

                // Hide the loading spinner
                hideLoadingSpinner();
                
                // Explicitly ensure winners section is visible
                const winnersSection = document.getElementById('winners-section');
                if (winnersSection) {
                    winnersSection.style.display = 'block';
                    winnersSection.style.visibility = 'visible';
                    winnersSection.classList.add('always-visible');
                    console.log('REVEAL: Ensuring winners section visibility during reveal');
                }

                // Show the winner modal
                showWinnerModal();

                // Run the confetti animation (with reduced motion check)
                if (!CONFIG.reducedMotion) {
                    runConfetti();
                } else {
                    // For reduced motion, just show a simpler effect
                    runReducedMotionEffect();
                }

                // Show notification after a configurable delay
                setTimeout(function () {
                    // Use Filament notification API
                    if (typeof Livewire !== 'undefined') {
                        window.dispatchEvent(
                            new CustomEvent('notify', {
                                detail: {
                                    icon: 'heroicon-o-check-circle',
                                    iconColor: 'success',
                                    timeout: CONFIG.notificationDuration,
                                    title: 'Winner Selected!',
                                    body: document.querySelector('.main-winner .text-success-800')?.innerText || 'Congratulations to the winner!'
                                }
                            })
                        );
                    }
                    // Record sequence completion
                    PERF.sequenceComplete = performance.now();
                    console.log('Total sequence time: ' + (PERF.sequenceComplete - PERF.drawStart) + 'ms');
                }, CONFIG.notificationDelay);
            }

            // Function to run the confetti animation
            function runConfetti() {

                // First burst - initial explosion
                const duration = CONFIG.confettiDuration; // Use configurable duration
                const animationEnd = Date.now() + duration;
                const defaults = {
                    startVelocity: 45,
                    spread: 360,
                    ticks: 70,
                    zIndex: 9999,
                    colors: ['#e9088A', '#51d13A', '#f9a600', '#00b7f9', '#FC3a60', '#FFD700', '#FF4500'],
                    shapes: ['circle', 'square'],
                    scalar: 1.2,
                    disableForReducedMotion: CONFIG.reducedMotion // Respect reduced motion preference
                };

                function randomInRange(min, max) {
                    return Math.random() * (max - min) + min;
                }

                // Main burst interval
                const interval = setInterval(function () {
                    const timeLeft = animationEnd - Date.now();

                    if (timeLeft <= 0) {
                        return clearInterval(interval);
                    }

                    const particleCount = 50 * (timeLeft / duration);

                    // Confetti from multiple angles for a more immersive effect
                    confetti({
                        ...defaults,
                        particleCount,
                        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
                    });

                    confetti({
                        ...defaults,
                        particleCount,
                        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
                    });

                    confetti({
                        ...defaults,
                        particleCount: particleCount * 0.5,
                        origin: { x: randomInRange(0.4, 0.6), y: Math.random() - 0.2 }
                    });
                }, 250);

                // Add cannon shots
                setTimeout(function () {
                    confetti({
                        particleCount: 100,
                        spread: 70,
                        origin: { y: 0.6, x: 0.1 },
                        colors: ['#e9088A', '#FC3a60']
                    });
                }, 500);

                setTimeout(function () {
                    confetti({
                        particleCount: 100,
                        spread: 70,
                        origin: { y: 0.6, x: 0.9 },
                        colors: ['#51d13A', '#00b7f9']
                    });
                }, 900);

                // Add some falling confetti
                setTimeout(function () {
                    confetti({
                        particleCount: 200,
                        angle: 60,
                        spread: 70,
                        origin: { x: 0 },
                        colors: ['#e9088A', '#51d13A', '#f9a600', '#00b7f9', '#FC3a60', '#FFD700'],
                        shapes: ['circle', 'square', 'star'],
                        scalar: 1.5
                    });
                    confetti({
                        particleCount: 200,
                        angle: 120,
                        spread: 70,
                        origin: { x: 1 },
                        colors: ['#e9088A', '#51d13A', '#f9a600', '#00b7f9', '#FC3a60', '#FFD700'],
                        shapes: ['circle', 'square', 'star'],
                        scalar: 1.5
                    });
                }, 2000);

                // Final burst after a delay
                setTimeout(function () {
                    confetti({
                        particleCount: 300,
                        spread: 160,
                        origin: { y: 0.6 },
                        colors: ['#e9088A', '#51d13A', '#f9a600', '#00b7f9', '#FC3a60', '#FFD700', '#FF4500'],
                        shapes: ['circle', 'square', 'star'],
                        scalar: 1.8,
                        drift: 2,
                        disableForReducedMotion: true
                    });
                }, 3500);
                
                // Add glitter effect around the modal
                setTimeout(function() {
                    // Left side glitter
                    confetti({
                        particleCount: 40,
                        angle: 60,
                        spread: 20,
                        origin: { x: 0.2, y: 0.5 },
                        colors: ['#FFD700', '#FFFFFF', '#e9088A'],
                        ticks: 200,
                        gravity: 0.1,
                        scalar: 0.8,
                        drift: 0
                    });
                    
                    // Right side glitter
                    confetti({
                        particleCount: 40,
                        angle: 120,
                        spread: 20,
                        origin: { x: 0.8, y: 0.5 },
                        colors: ['#FFD700', '#FFFFFF', '#e9088A'],
                        ticks: 200,
                        gravity: 0.1,
                        scalar: 0.8,
                        drift: 0
                    });
                }, 4500);
            }

            // Function for reduced motion alternative effect
            function runReducedMotionEffect() {
                // Simple highlight effect for the winner display
                const winnerBox = document.querySelector('.main-winner .bg-success-50');
                if (winnerBox) {
                    winnerBox.style.transition = 'box-shadow 0.5s ease-in-out';
                    winnerBox.style.boxShadow = '0 0 15px 5px rgba(16, 185, 129, 0.7)';
                }
            }

            // Function to log performance data
            function logPerformanceData() {
                const data = {
                    totalTime: PERF.sequenceComplete - PERF.drawStart,
                    loadingTime: PERF.loadingShown - PERF.drawStart,
                    revealDelay: PERF.winnerRevealed - PERF.drawStart,
                    browser: navigator.userAgent,
                    reducedMotion: CONFIG.reducedMotion,
                    timestamp: new Date().toISOString()
                };

                console.table(data);

                // This could be extended to send analytics data to the server
                // for aggregating timing information across users
            }

            // Initialize when page loads
            document.addEventListener('DOMContentLoaded', function () {
                // Create the loading overlay in advance
                createLoadingOverlay();

                // Make sure winners section is visible by default
                const winnersSection = document.getElementById('winners-section');
                if (winnersSection) {
                    winnersSection.style.display = 'block';
                }

                // Add fade-in animation style
                const style = document.createElement('style');
                style.textContent = '@keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }';
                document.head.appendChild(style);

                // Check if winner is already displayed
                const winnerDisplay = document.querySelector('.winner-display');
                if (winnerDisplay) {
                    // Store the element for later use
                    winnerDisplayElement = winnerDisplay;

                    // If this is a page reload with winner already shown (not a fresh draw)
                    // then show it immediately without the dramatic buildup
                    if (!window.location.href.includes('fresh')) {
                        if (!CONFIG.reducedMotion) {
                            runConfetti();
                        } else {
                            runReducedMotionEffect();
                        }
                    }
                }

                // Add click handler to the draw button to show loading immediately
                const drawButton = document.querySelector('button[type=\'submit\']');
                if (drawButton) {
                    drawButton.addEventListener('click', function () {
                        // Show loading spinner immediately when button is clicked
                        showLoadingSpinner();
                        // Start performance tracking
                        PERF.drawStart = performance.now();
                    });
                }

                // Check for reduced motion preference changes
                window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', function (e) {
                    CONFIG.reducedMotion = e.matches;
                    console.log('Reduced motion preference changed to: ' + CONFIG.reducedMotion);
                });
            });
        </script>
        <div class="space-y-6">
            <!-- Maximum Winners Information -->
            <x-filament::section>
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Campaign Status</h3>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            This campaign allows a maximum of <span
                                class="font-semibold text-primary-600 dark:text-primary-400">{{ $record->max_winners }}</span>
                            winners.
                        </p>
                    </div>
                    <div class="text-right">
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $record->winners->count() >= $record->max_winners ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' }}">
                            {{ $record->winners->count() }} / {{ $record->max_winners }} Winners
                        </span>
                    </div>
                </div>
            </x-filament::section>

            <x-filament::section>
                <div class="text-center">
                    <h2 class="text-xl font-bold">Campaign: {{ $record->name }}</h2>
                    <p class="text-sm text-gray-500">
                        Start Date: {{ $record->start_date->format('M d, Y') }} |
                        End Date: {{ $record->end_date->format('M d, Y') }} |
                        Draw Date: {{ $record->draw_date->format('M d, Y') }}
                    </p>
                </div>
            </x-filament::section>

            <x-filament::section>
                <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div
                        class="rounded-lg bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-800 dark:ring-white/10">
                        <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">Eligible Transactions
                        </h3>
                        <div class="mt-2 flex items-baseline">
                            <div class="text-4xl font-semibold text-primary-600 dark:text-primary-400">
                                {{ $eligibleTransactionCount }}
                            </div>
                        </div>
                        <p class="mt-3 text-sm text-gray-500 dark:text-gray-400">
                            Total number of transactions within campaign date range.
                        </p>
                    </div>

                    <div
                        class="rounded-lg bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-800 dark:ring-white/10">
                        <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">Eligible Coupons
                        </h3>
                        <div class="mt-2 flex items-baseline">
                            <div class="text-4xl font-semibold text-primary-600 dark:text-primary-400">
                                {{ $eligibleCouponCount }}
                            </div>
                        </div>
                        <p class="mt-3 text-sm text-gray-500 dark:text-gray-400">
                            Total number of active coupons eligible for the draw.
                        </p>
                    </div>

                    <div
                        class="rounded-lg bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-800 dark:ring-white/10">
                        <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">Winners</h3>
                        <div class="mt-2 flex items-baseline">
                            <div class="text-4xl font-semibold text-primary-600 dark:text-primary-400">
                                {{ $winners->count() }}
                            </div>
                        </div>
                        <p class="mt-3 text-sm text-gray-500 dark:text-gray-400">
                            Total number of winners drawn in this campaign.
                        </p>
                    </div>
                </div>
            </x-filament::section>

            @if($winner)
                <!-- Hidden winner display section (used as data source for the modal) -->
                <div class="hidden main-winner">
                    <div class="text-center">
                        <div class="mx-auto max-w-md rounded-lg bg-success-50 p-6 shadow-sm dark:bg-success-950/50">
                            <!-- Winner information with enhanced styling -->
                            <div class="mt-4 text-2xl font-bold text-success-800 dark:text-success-200">
                                {{ $winner->name }}
                            </div>
                            <div class="mt-2 text-sm text-success-600 dark:text-success-400">
                                {{ \App\Helpers\MaskedEmail::generate($winner->email) }}
                            </div>
                            <div class="mt-4 text-2xl font-bold text-success-800 dark:text-success-200">
                                {{ $winner->coupon->code }}
                            </div>
                            <div class="mt-2 text-sm text-success-600 dark:text-success-400">
                                Won at: {{ $winner->won_at->format('M d, Y H:i:s') }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Always visible recent winners section - completely separate from the winner display -->
                <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm ring-1 ring-gray-950/5 dark:ring-white/10">
                    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Recent Winners</h3>
                    </div>
                    <div class="p-4 always-visible" id="winners-section" style="display: block !important; visibility: visible !important">
                        @if($winners->isEmpty())
                            <div class="text-center text-sm text-gray-500 dark:text-gray-400 py-4">
                                No winners have been drawn yet for this campaign.
                            </div>
                        @else
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($winners as $winner)
                                    <div
                                        class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition duration-150">
                                        <div class="p-4">
                                            <div class="flex items-center justify-between mb-3">
                                                <div class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                                    {{ $winner->name }}
                                                </div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                                    {{ $winner->won_at->format('M d, Y') }}
                                                </div>
                                            </div>

                                            <div class="flex items-center mb-2">
                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                    class="h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" fill="none"
                                                    viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                                </svg>
                                                <div class="text-sm text-gray-600 dark:text-gray-300">
                                                    {{ \App\Helpers\MaskedEmail::generate($winner->email) }}
                                                </div>
                                            </div>

                                            <div class="flex items-center mb-2">
                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                    class="h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" fill="none"
                                                    viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                                                </svg>
                                                <div
                                                    class="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-primary-600 dark:text-primary-400">
                                                    {{ $winner->coupon->code }}
                                                </div>
                                            </div>

                                            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
                                                    viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                                {{ $winner->won_at->format('H:i:s') }}
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
</x-filament-panels::page>
