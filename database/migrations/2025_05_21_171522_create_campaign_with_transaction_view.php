<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the view if it exists (for SQLite compatibility)
        DB::statement('DROP VIEW IF EXISTS campaign_with_transaction');

        // Create the view
        DB::statement(<<<'SQL'
            CREATE VIEW campaign_with_transaction AS
            SELECT
                campaigns.*,
                (
                    SELECT COUNT(*)
                    FROM transactions
                    WHERE transactions.transaction_date
                    BETWEEN campaigns.start_date AND campaigns.end_date
                ) AS transaction_count
            FROM campaigns
        SQL);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP VIEW IF EXISTS campaign_with_transaction');
    }
};
