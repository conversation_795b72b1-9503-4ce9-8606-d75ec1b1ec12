<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, drop the foreign key constraints that reference the transactions table
        Schema::table('coupons', function (Blueprint $table) {
            $table->dropForeign(['transaction_id']);
        });

        // Drop the existing transactions table
        Schema::dropIfExists('transactions');

        // Create the transactions view using the provided query
        $legacyDatabase = env('DB_DATABASE_LEGACY', 'vidingco_db_production');

        DB::statement("
            CREATE VIEW transactions AS
            SELECT
                us.id AS id,
                ts.id AS tenant_id,
                us.email AS email,
                us.name AS name,
                min(tbp.paid_at) AS transaction_from,
                max(tbp.paid_at) AS transaction_to,
                sum(tbp.harga) AS transaction_sum,
                'success' AS transaction_status,
                us.id AS transaction_id,
                min(tbp.paid_at) AS transaction_date,
                NOW() AS created_at,
                NOW() AS updated_at
            FROM {$legacyDatabase}.users us
            JOIN {$legacyDatabase}.tb_steppers ts ON us.id = ts.id_user
            JOIN {$legacyDatabase}.tb_payment_backup tbp ON tbp.steppers_id = ts.id
            WHERE tbp.status = 'success'
            AND tbp.paid_at IS NOT NULL
            GROUP BY us.id, us.email, us.name, ts.id
            ORDER BY sum(tbp.harga) DESC
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the view
        DB::statement('DROP VIEW IF EXISTS transactions');

        // Recreate the original transactions table
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->unsignedBigInteger('transaction_id')->unique();
            $table->date('transaction_date');
            $table->unsignedBigInteger('transaction_sum');
            $table->timestamps();
        });

        // Add back the indexes
        Schema::table('transactions', function (Blueprint $table) {
            $table->index('email', 'transactions_email_index');
            $table->index('transaction_date', 'transactions_date_index');
        });

        // Recreate the foreign key constraint
        Schema::table('coupons', function (Blueprint $table) {
            $table->foreign('transaction_id')->references('transaction_id')->on('transactions')->cascadeOnDelete();
        });
    }
};
