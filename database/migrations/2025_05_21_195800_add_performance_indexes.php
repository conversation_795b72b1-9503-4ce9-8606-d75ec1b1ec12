<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes to transactions table
        Schema::table('transactions', function (Blueprint $table) {
            $table->index('email', 'transactions_email_index');
            $table->index('transaction_date', 'transactions_date_index');
        });

        // Add indexes to coupons table
        Schema::table('coupons', function (Blueprint $table) {
            $table->index('campaign_id', 'coupons_campaign_id_index');
            $table->index('transaction_id', 'coupons_transaction_id_index');
            $table->index('expired', 'coupons_expired_index');
            $table->index(['campaign_id', 'expired'], 'coupons_campaign_expired_index');
        });

        // Add indexes to winners table
        Schema::table('winners', function (Blueprint $table) {
            $table->index('campaign_id', 'winners_campaign_id_index');
            $table->index('email', 'winners_email_index');
            $table->index('created_at', 'winners_created_at_index');
        });

        // Add indexes to campaigns table
        Schema::table('campaigns', function (Blueprint $table) {
            $table->index('status', 'campaigns_status_index');
            $table->index('draw_date', 'campaigns_draw_date_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove indexes from transactions table
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropIndex('transactions_email_index');
            $table->dropIndex('transactions_date_index');
        });

        // Remove indexes from coupons table
        Schema::table('coupons', function (Blueprint $table) {
            $table->dropIndex('coupons_campaign_id_index');
            $table->dropIndex('coupons_transaction_id_index');
            $table->dropIndex('coupons_expired_index');
            $table->dropIndex('coupons_campaign_expired_index');
        });

        // Remove indexes from winners table
        Schema::table('winners', function (Blueprint $table) {
            $table->dropIndex('winners_campaign_id_index');
            $table->dropIndex('winners_email_index');
            $table->dropIndex('winners_created_at_index');
        });

        // Remove indexes from campaigns table
        Schema::table('campaigns', function (Blueprint $table) {
            $table->dropIndex('campaigns_status_index');
            $table->dropIndex('campaigns_draw_date_index');
        });
    }
};
