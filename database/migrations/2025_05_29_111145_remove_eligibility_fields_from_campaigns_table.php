<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('campaigns', function (Blueprint $table) {
            $table->dropColumn(['min_transaction_amount', 'purchase_recency_days']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('campaigns', function (Blueprint $table) {
            $table->integer('min_transaction_amount')->nullable()->default(0)->comment('Minimum transaction amount required for eligibility');
            $table->integer('purchase_recency_days')->nullable()->default(0)->comment('Number of days within which a purchase must have been made to be eligible');
        });
    }
};
