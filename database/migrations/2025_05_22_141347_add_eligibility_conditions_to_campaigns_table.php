<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('campaigns', function (Blueprint $table) {
            $table->integer('min_transaction_amount')->nullable()->default(0)->comment('Minimum transaction amount required for eligibility');
            $table->json('eligible_payment_methods')->nullable()->comment('JSON array of payment methods that are eligible');
            $table->json('eligible_transaction_types')->nullable()->comment('JSON array of transaction types that are eligible');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('campaigns', function (Blueprint $table) {
            $table->dropColumn('min_transaction_amount');
            $table->dropColumn('eligible_payment_methods');
            $table->dropColumn('eligible_transaction_types');
        });
    }
};
