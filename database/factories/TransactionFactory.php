<?php

namespace Database\Factories;

use App\Helpers\UniqueNumber;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Transaction>
 */
class TransactionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $transaction_date = $this->faker->dateTimeBetween('-3 month', 'now');
        $transaction_id = UniqueNumber::generate('transactions', 'id', 8);

        return [
            'name' => "{$this->faker->firstNameFemale()} & {$this->faker->firstNameMale()}",
            'email' => $this->faker->safeEmail(),
            'transaction_id' => $transaction_id,
            'transaction_date' => $transaction_date,
            'transaction_sum' => ceil(rand(300000, 10000000) / 1000) * 1000,
        ];
    }
}
