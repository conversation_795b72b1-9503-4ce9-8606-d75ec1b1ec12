<?php

namespace Database\Seeders;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Seeder;

class TransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (User::count() === 0) {
            User::create([
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => 'password',
            ]);
        }

        Transaction::factory(10000)->create();
    }
}
