# Dynamic Email System for Campaign Coupons

The campaign coupon email system now supports dynamic content that can be customized for each campaign through the Filament admin interface.

## Features

- **Custom Email Subject**: Each campaign can have its own email subject line
- **Custom Email Body**: Rich text editor for email content with placeholder support
- **Placeholder System**: Dynamic content replacement with campaign and user data
- **Fallback Content**: Default content is used if no custom content is provided
- **Rich Text Support**: HTML formatting support through Filament's RichEditor

## How to Use

### 1. Creating/Editing a Campaign

1. Go to the Filament admin panel
2. Navigate to Campaigns
3. Create a new campaign or edit an existing one
4. Expand the "Email Configuration" section
5. Customize the email subject and body content
6. Use placeholders to insert dynamic content

### 2. Available Placeholders

You can use these placeholders in both email subject and body:

| Placeholder | Description | Example Output |
|-------------|-------------|----------------|
| `[NAME]` | User's name | "<PERSON>" |
| `[COUPON_COUNT]` | Number of coupons | "3" |
| `[COUPONS]` | HTML formatted coupon codes | Styled coupon display |
| `[TRANSACTION_DATE]` | User's transaction date | "15 May 2024" |
| `[START_DATE]` | Campaign start date | "01 May 2024" |
| `[END_DATE]` | Campaign end date | "31 May 2024" |
| `[DRAW_DATE]` | Campaign draw date | "05 Jun 2024" |
| `[CAMPAIGN_NAME]` | Campaign name | "Undian Emas 2024" |
| `[CAMPAIGN_DESCRIPTION]` | Campaign description | "Special gold raffle" |

### 3. Example Email Templates

#### Subject Examples

```
🎉 Selamat [NAME]! Kupon [CAMPAIGN_NAME] Sudah Siap!
🌙 Ramadan Mubarak [NAME]! [COUPON_COUNT] Kupon Menanti!
🎊 [NAME], Anda Mendapat [COUPON_COUNT] Kupon Undian!
```

#### Body Example

```
Halo [NAME],

Terima kasih telah berpartisipasi dalam [CAMPAIGN_NAME]! 🎉

Kami dengan senang hati memberikan [COUPON_COUNT] kupon undian untuk Anda:

[COUPONS]

📅 Tanggal Transaksi: [TRANSACTION_DATE]
🎯 Campaign: [CAMPAIGN_NAME]
📝 Deskripsi: [CAMPAIGN_DESCRIPTION]

⏰ Periode Campaign: [START_DATE] - [END_DATE]
🎊 Pengumuman Pemenang: [DRAW_DATE]

Semoga beruntung!

Salam hangat,
Tim Viding.co
https://viding.co
```

## Technical Implementation

### Database Schema

Two new fields have been added to the `campaigns` table:

- `email_subject` (string, nullable) - Custom email subject
- `email_body` (longText, nullable) - Custom email body content

### Model Methods

The `Campaign` model includes:

- `replacePlaceholders()` - Replaces placeholders with actual values
- `getEmailSubjectAttribute()` - Returns custom or default subject
- `getEmailBodyAttribute()` - Returns custom or default body

### Mail Class

The `CampaignCoupons` mail class:

- Uses dynamic subject from campaign
- Processes email body with placeholder replacement
- Generates HTML for coupon display
- Passes processed data to email template

## Commands

### Preview Email

```bash
php artisan email:preview-campaign-coupons
```

Shows how the email will look with sample data.

### Create Sample Campaign

```bash
php artisan campaign:create-sample
```

Creates a sample campaign with custom email content.

## Default Content

If no custom email content is provided, the system uses default Indonesian content that matches the original specification.

## Best Practices

1. **Test Email Content**: Use the preview command to test your email templates
2. **Use Placeholders**: Always use placeholders instead of hardcoded values
3. **Keep It Simple**: Avoid complex HTML in the rich text editor
4. **Mobile Friendly**: Keep email content concise for mobile viewing
5. **Brand Consistency**: Maintain consistent tone and branding across campaigns

## Troubleshooting

- **Placeholders not replaced**: Check placeholder spelling and case sensitivity
- **HTML not rendering**: Ensure proper HTML syntax in rich text editor
- **Email not sending**: Check campaign status and email configuration
- **Missing data**: Verify all required campaign fields are filled

## Migration

Existing campaigns will use the default email content. To customize:

1. Edit the campaign in Filament
2. Add custom email subject and body
3. Save the campaign
4. Test with the preview command
