# Technical Architecture

## System Components

### Core Services

1. **DrawService**
   - `performDraw()`: Executes game show-style draw sequence
   - `validateUserParticipation()`: Checks user eligibility
   - `getWinners()`: Retrieves campaign winners

2. **CouponGenerationService**
   - `generateForTransaction()`: Creates coupons based on transaction amount
   - `generateForCampaign()`: Bat<PERSON> processes all eligible transactions
   - `COUPON_THRESHOLD`: Rp300,000 per coupon constant

3. **CampaignService**
   - Manages campaign lifecycle and rules

## Database Schema

```mermaid
classDiagram
    class Campaign {
        +id
        +name
        +start_date
        +end_date
        +prize_details
    }
    
    class Transaction {
        +id
        +amount
        +user_id
        +date
    }
    
    class Coupon {
        +id
        +code
        +expired
        +transaction_id
        +campaign_id
    }
    
    class Winner {
        +id
        +won_at
        +coupon_id
        +campaign_id
    }
    
    Campaign "1" -- "*" Coupon
    Transaction "1" -- "*" Coupon
    Coupon "1" -- "1" Winner
```

## UI Layer

### Admin Interface (Filament)
- **Campaign Management**
  - Create/edit campaigns
  - View transaction/coupon statistics
  - Manage winners

- **Draw Interface**
  - Dedicated DrawCampaign page
  - One-click draw initiation
  - Real-time winner display
[View Full Mockup](./mockups/draw_interface.md)
┌─────────────────────────────────────────────────────┐
│                   DRAW CONTROL PANEL                │
├─────────────────────────────────────────────────────┤
│ Campaign: [Summer Sale ▼]      [ Start Draw ]       │
└─────────────────────────────────────────────────────┘

### Game Show Experience
```mermaid
sequenceDiagram
    participant Admin
    participant UI
    participant Service
    
    Admin->>UI: Initiate Draw
    UI->>Service: performDraw()
    Service-->>UI: Winner Data
    UI->>UI: Play sound effects (6s)
    UI->>UI: Trigger confetti animation
    UI->>UI: Reveal winner
    UI->>Admin: Show notification
```
[View Full Sequence](./mockups/draw_interface.md#winner-display)
┌─────────────────────────────────────────────────────┐
│                   WINNER DISPLAY                    │
├─────────────────────────────────────────────────────┤
│                   jo****<EMAIL>               │
│                   Won Rp5,000,000                   │
└─────────────────────────────────────────────────────┘

### Key UI Components
1. **Draw Control Panel**
   - Start/stop draw buttons
   - Campaign selector
   
2. **Winner Display**
   - Animated reveal sequence
   - Masked email display (jo****<EMAIL>)
   
3. **Recent Winners Table**  
[Full Mockup](./mockups/winners_table.md)
┌───────────┬───────────────────────┬────────────────┤
│ 21/05/25  │ jo****<EMAIL>   │ Rp5,000,000    │
└───────────┴───────────────────────┴────────────────┘

4. **Responsive Design**
   - Works on desktop/tablet/mobile
   - Reduced motion support
   - **Mobile Responsive View**  
[Full Mockup](./mockups/mobile_view.md)
┌──────────────────────────────┐
│ [ START DRAW ]               │
├──────────────────────────────┤
│    jo****<EMAIL>       │
│    Won Rp5,000,000           │
└──────────────────────────────┘

## Data Flow

1. **Transaction Processing**
   - Transaction received → CouponGenerationService creates coupons
   - 1 coupon per Rp300,000 spent

2. **Draw Execution**
   - DrawService selects random eligible coupon
   - Coordinates sound/visual effects
   - Marks winner and expires related coupons

3. **Winner Management**
   - Winner record created
   - Notification triggered
   - Available via admin interface
