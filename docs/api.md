# API Reference

## Endpoints

### Campaigns
- `GET /api/campaigns` - List all campaigns
- `POST /api/campaigns` - Create new campaign

### Draws
- `POST /api/campaigns/{id}/draw` - Perform a draw
- `GET /api/campaigns/{id}/winners` - List winners

### Coupons
- `GET /api/transactions/{id}/coupons` - View generated coupons

## Responses

All responses follow JSON:API format with:
- Data object
- Included relationships
- Error handling

## Authentication

Bearer token authentication required for all endpoints
