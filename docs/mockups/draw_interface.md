# Draw Interface Mockup

```
┌─────────────────────────────────────────────────────┐
│                   DRAW CONTROL PANEL                │
├─────────────────────────────────────────────────────┤
│ Campaign: [Summer Sale ▼]      [ Start Draw ]       │
│                                                     │
│ Eligible Coupons: 1,428                             │
│ Previous Winners: 23                                │
└─────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────┐
│                   WINNER DISPLAY                    │
├─────────────────────────────────────────────────────┤
│                                                     │
│                   CONGRATULATIONS!                  │
│                                                     │
│                   jo****<EMAIL>               │
│                   Won Rp5,000,000                   │
│                                                     │
│           (Confetti animation displayed)            │
│                                                     │
└─────────────────────────────────────────────────────┘
```

Key Elements:
- Dropdown campaign selector
- Start draw button with prominent styling
- Statistics display
- Central winner reveal area
- Masked email display
- Prize amount shown
