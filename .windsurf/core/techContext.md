# Technology Context: Game Show-style LuckyDraw Engine

## Technology Stack

### Backend Framework
- **<PERSON><PERSON> 12**: The latest version of the Laravel PHP framework
- **PHP 8.2+**: Required for Laravel 12 compatibility

### Admin Interface
- **Filament 3**: Admin panel builder for Laravel applications
- **Livewire**: For interactive UI components within Filament
- **Alpine.js**: For JavaScript interactions in the UI
- **Tailwind CSS**: For styling Filament components

### Database
- **MySQL**: Existing database with transactions and coupons tables
- **Eloquent ORM**: <PERSON><PERSON>'s database ORM for model interactions

### Optional Components
- **Laravel Echo**: For real-time broadcasting (optional enhancement)
- **Pusher**: Real-time event service (optional enhancement)
- **Laravel Horizon**: For queue monitoring if using queued jobs

## Development Environment

### Local Development
- PHP 8.2+ with required extensions
- Composer for PHP dependency management
- Node.js and NPM for frontend asset compilation
- MySQL database server
- <PERSON><PERSON>t, <PERSON>vel Sail, or similar local development environment

### Version Control
- Git for source code management

## Deployment Considerations

### Server Requirements
- PHP 8.2+ with required extensions
- MySQL 8.0+
- Composer
- Node.js and NPM for asset compilation
- Web server (Nginx or Apache)

### Environment Variables
- Database connection details
- Application environment settings
- Optional: Pusher credentials if using real-time features

## Key Dependencies

### Core Laravel Packages
- **filament/filament**: Admin panel framework
- **livewire/livewire**: Interactive component framework
- **laravel/framework**: Core Laravel framework

### Utility Packages
- **spatie/laravel-permission**: For role and permission management
- **spatie/laravel-activitylog**: For audit logging of important actions
- **barryvdh/laravel-debugbar**: For development debugging

### Optional Packages
- **laravel/horizon**: Queue monitoring dashboard
- **laravel/echo**: Client-side library for WebSockets
- **pusher/pusher-php-server**: Server-side Pusher integration

## Integration Points

### Existing Database Schema

#### transactions Table
- transaction_id (primary key)
- transaction_sum (decimal)
- transaction_date (datetime)
- user_id or similar (to identify the customer)
- other transaction-related fields

#### coupons Table
- id (primary key)
- transaction_id (foreign key)
- code (unique string)
- expired (boolean flag, 0=active, 1=expired)
- other coupon-related fields

### New Schema Extensions
- campaigns table for campaign management
- winners table for tracking draw winners

## Development Workflow

1. Set up Laravel 12 project with Filament 3
2. Configure database connection to existing schema
3. Create models for existing and new tables
4. Implement Filament resources and pages
5. Develop core services for business logic
6. Build the draw engine functionality
7. Implement optional enhancements if required
8. Test all functionality thoroughly
9. Deploy to production environment

## Performance Considerations

- Index key fields in database tables
- Cache frequently accessed data
- Queue resource-intensive operations like coupon generation
- Optimize database queries for large transaction sets
- Consider pagination for large data sets in the admin interface
