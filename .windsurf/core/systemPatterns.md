# System Patterns: Game Show-style LuckyDraw Engine

## Architecture Overview

The LuckyDraw Engine follows a standard Laravel MVC architecture with Filament for the admin interface. The system is designed around several core entities: Campaigns, Transactions, Coupons, and Winners.

## Design Patterns

### Repository Pattern

-   Used for data access abstraction
-   Separate repositories for Campaign, Transaction, Coupon, and Winner entities
-   Allows for clean separation between business logic and data access

### Service Layer Pattern

-   Business logic encapsulated in dedicated service classes
-   CouponGenerationService for handling coupon creation logic
-   DrawService for managing the winner selection process
-   CampaignService for campaign management operations

### Observer Pattern

-   Laravel Observers used to monitor model events
-   Transaction observer to trigger coupon generation
-   Winner observer to handle post-win actions (expiring other coupons)

### Factory Pattern

-   Used for creating complex objects like Coupons
-   UniqueCouponFactory for generating unique coupon codes

### Strategy Pattern

-   Different strategies for coupon generation and winner selection
-   Allows for future expansion with different rules or algorithms

## Data Flow

1. **Campaign Creation**:

    - Admin creates campaign with parameters via Filament
    - Campaign stored in database with status "active"

2. **Coupon Generation**:

    - System queries transactions within campaign date range
    - For each transaction, calculates number of coupons based on amount
    - Generates unique coupon codes and links to transaction
    - Stores coupons with "active" status

3. **Draw Execution**:

    - Admin initiates draw via Filament interface
    - System selects random coupon from active pool
    - Winner record created linking user and winning coupon
    - All other coupons from same user for same campaign marked as expired

4. **Winner Display**:
    - Winner information formatted for display
    - Optional: Event broadcasting for real-time updates
    - Winner board updated with new winner

## Component Diagram

```
+-------------------+      +-------------------+      +-------------------+
|                   |      |                   |      |                   |
|  Filament Admin   |<---->|  Laravel Core    |<---->|  MySQL Database   |
|  Interface        |      |  Services         |      |                   |
|                   |      |                   |      |                   |
+-------------------+      +-------------------+      +-------------------+
         ^                          ^
         |                          |
         v                          v
+-------------------+      +-------------------+
|                   |      |                   |
|  API Endpoints    |      |  Event            |
|  (Optional)       |      |  Broadcasting     |
|                   |      |  (Optional)       |
+-------------------+      +-------------------+
```

## Key Interfaces

### CouponGeneratorInterface

```php
interface CouponGeneratorInterface
{
    public function generateForTransaction(Transaction $transaction, Campaign $campaign): Collection;
}
```

### DrawServiceInterface

```php
interface DrawServiceInterface
{
    public function performDraw(Campaign $campaign): ?Winner;
    public function expireRelatedCoupons(Winner $winner): void;
}
```

### CampaignServiceInterface

```php
interface CampaignServiceInterface
{
    public function getActiveCampaigns(): Collection;
    public function getEligibleTransactions(Campaign $campaign): Collection;
}
```

## Database Schema Extensions

Building upon the existing transactions and coupons tables, we'll add:

### campaigns Table

-   id (primary key)
-   name (string)
-   description (text)
-   start_date (date)
-   end_date (date)
-   draw_date (date)
-   status (enum: active, completed, cancelled)
-   created_at, updated_at (timestamps)

### winners Table

-   id (primary key)
-   campaign_id (foreign key)
-   coupon_id (foreign key)
-   user_id (foreign key or value from transaction)
-   won_at (datetime)
-   created_at, updated_at (timestamps)

## Security Considerations

-   Role-based access control for admin functions
-   Audit logging for all draw-related actions
-   Validation of all input parameters
-   Protection against manipulation of the draw process
-   Rate limiting on API endpoints
