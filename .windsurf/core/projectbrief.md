# Project Brief: Game Show-style LuckyDraw Engine

## Overview

This project involves developing a Game Show-style LuckyDraw Engine using Laravel 12 and Filament 3. The system will be integrated with an existing MySQL database schema that includes transactions and coupons tables. The primary purpose is to create a platform for managing promotional campaigns where customers earn coupons based on their transaction amounts, which can then be used in lucky draws to determine winners.

## Core Objectives

1. Create a comprehensive Campaign Management system using Laravel 12 and Filament 3
2. Implement automatic coupon generation based on transaction amounts (1 coupon per Rp300,000 spent)
3. Develop a random draw engine that selects winners while enforcing business rules
4. Build a complete admin interface using Filament for managing all aspects of the system
5. Create a winner display system for live events

## Key Features

### Campaign Configuration
- Admin-configurable campaigns with start/end dates for transaction inclusion
- Configurable draw dates for executing live draws

### Coupon Generation
- Automatic coupon generation based on transaction amounts
- Prevention of duplicate coupon generation
- Unique coupon code generation system

### Game Show Draw Engine
- Random winner selection from active coupons
- Automatic expiration of a winner's remaining coupons
- Enforcement of one winner per user per campaign rule

### Filament Admin Panel
- Campaign management interface
- Transaction overview and filtering
- Coupon management and status tracking
- Draw console with manual triggering
- Winner board with export functionality

### Optional Enhancements
- Public API for live winner display
- Real-time broadcasting of draw results
- Visual effects for the draw process

## Success Criteria

1. Full integration with existing database schema
2. Correct implementation of business rules for coupon generation and winner selection
3. Intuitive admin interface built with Filament 3
4. Reliable and fair random selection process
5. Proper documentation of all components and processes
6. Clean, maintainable code following Laravel best practices
