# Email Masking Verification Plan

## Objective

Verify that the email masking functionality works correctly with various email formats to ensure consistent privacy protection across all scenarios.

## Current Implementation

The current implementation in `DrawCampaign.php` masks emails as follows:
- Shows first 2 characters of the username
- Replaces middle characters with asterisks
- Shows the last character of the username
- Keeps the domain intact
- For usernames shorter than 4 characters, no masking is applied

## Test Cases

| Test ID | Email Format | Example | Expected Result | Actual Result | Status |
|---------|--------------|---------|-----------------|--------------|--------|
| E1 | Standard email | <EMAIL> | jo****<EMAIL> | | |
| E2 | Short username (≤ 3 chars) | <EMAIL> | <EMAIL> | | |
| E3 | Very long username | <EMAIL> | jo*************************<EMAIL> | | |
| E4 | Email with numbers | <EMAIL> | jo****<EMAIL> | | |
| E5 | Email with special chars | <EMAIL> | jo**********<EMAIL> | | |
| E6 | Email with underscores | <EMAIL> | jo************<EMAIL> | | |
| E7 | Email with dots | <EMAIL> | jo************<EMAIL> | | |
| E8 | International domain | john.doe@例子.com | jo****e@例子.com | | |
| E9 | Uppercase email | <EMAIL> | JO****<EMAIL> | | |
| E10 | Mixed case email | <EMAIL> | Jo****<EMAIL> | | |
| E11 | Invalid email (no @) | johndoe | Error handling? | | |
| E12 | Empty email | "" | Error handling? | | |

## Potential Issues to Check

1. **Edge Cases Handling**:
   - Very short usernames (1-3 characters)
   - Very long usernames (50+ characters)
   - Empty strings
   - Malformed emails (missing @ or domain)

2. **Special Character Handling**:
   - Emails with dots, plus signs, or other special characters in username
   - International domains and Unicode characters

3. **Consistency**:
   - Ensure masking is applied consistently across all display locations
   - Check case sensitivity handling

## Implementation Improvements

Based on testing results, consider the following potential improvements:

1. **Enhanced Error Handling**:
   - Add validation to ensure the email contains an @ symbol
   - Gracefully handle empty strings

2. **Configurable Masking**:
   - Make the number of visible characters configurable
   - Consider masking part of the domain for additional privacy

3. **Consistent Display**:
   - Ensure consistent masking across all places where emails are displayed
   - Add unit tests for the masking function

## Testing Procedure

1. Create a test script that calls the `getMaskedEmail()` function with each test case
2. Compare actual results with expected results
3. Document any discrepancies or issues
4. Implement necessary fixes
5. Re-test to verify fixes

## Test Script Implementation

Create a simple PHP script to test the masking function with all test cases:
