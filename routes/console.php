<?php

// Run the campaign:start command every minute to check for campaigns that need to be started
Schedule::command('campaign:start')->everyMinute();

// Run the job to generate coupons and send emails for running campaigns every 10 minutes
Schedule::command('queue:work --stop-when-empty')->everyTenMinutes();

// Check for campaigns that have reached their maximum winners and mark them as completed
Schedule::command('campaigns:check-completed')->daily();

