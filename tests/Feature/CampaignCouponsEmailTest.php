<?php

namespace Tests\Feature;

use App\Mail\CampaignCoupons;
use App\Models\Campaign;
use Carbon\Carbon;
use Tests\TestCase;

class CampaignCouponsEmailTest extends TestCase
{
    public function test_campaign_coupons_email_can_be_rendered()
    {
        // Create a mock campaign object
        $campaign = new Campaign([
            'name' => 'Test Campaign',
            'description' => 'Test campaign for email testing',
            'email_subject' => 'Test Subject for [NAME]',
            'email_body' => 'Hello [NAME], you have [COUPON_COUNT] coupons: [COUPONS]',
            'start_date' => Carbon::now()->subDays(10),
            'end_date' => Carbon::now()->addDays(10),
            'draw_date' => Carbon::now()->addDays(15),
            'status' => 'active',
            'running' => false,
            'max_winners' => 1,
            'coupon_threshold' => 300000,
        ]);

        // Create test coupons
        $coupons = [
            (object) [
                'id' => 1,
                'code' => 'CP12345678',
                'transaction_id' => 12345678,
                'campaign_id' => 1,
            ],
            (object) [
                'id' => 2,
                'code' => 'CP87654321',
                'transaction_id' => 12345678,
                'campaign_id' => 1,
            ],
        ];

        // Create user object
        $user = new \stdClass;
        $user->name = 'John Doe';
        $user->email = '<EMAIL>';
        $user->transaction_date = Carbon::now()->subDays(5);

        // Test that the email can be rendered without errors
        $mailable = new CampaignCoupons($user, $coupons, $campaign);

        $this->assertInstanceOf(CampaignCoupons::class, $mailable);

        // Test the subject line (should have placeholders replaced)
        $envelope = $mailable->envelope();
        $this->assertEquals('Test Subject for John Doe', $envelope->subject);

        // Test that the email content can be rendered
        $content = $mailable->content();
        $this->assertEquals('emails.campaign-coupons', $content->view);
    }
}
