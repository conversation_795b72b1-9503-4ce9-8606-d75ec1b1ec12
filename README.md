# LuckyDraw Engine

A game show-style lucky draw system built with Laravel 12 and Filament 3.

## Features

- 🎉 Immersive game show experience with sound effects and confetti animations
- 🔒 Privacy protection with email masking
- ⏱️ Dramatic timed reveal sequence
- 🎟️ Automatic coupon generation (1 per Rp300,000 spent)
- 🏆 Winner selection with one-winner-per-user-per-campaign rules

## Core Services

1. **DrawService** - Handles draw operations and winner selection
2. **CouponGenerationService** - Manages coupon creation and validation
3. **CampaignService** - Controls campaign lifecycle and rules

## Installation

```bash
composer install
npm install
php artisan migrate
php artisan db:seed
```

## Usage

1. Create a campaign in the Filament admin
2. Transactions automatically generate coupons
3. Run draws from the DrawCampaign page
4. View winners and statistics

## UI Preview

### Desktop Interface
![Draw Control Panel](docs/mockups/draw_interface.md)
┌─────────────────────────────────────────────────────┐
│                   DRAW CONTROL PANEL                │
├─────────────────────────────────────────────────────┤
│ Campaign: [Summer Sale ▼]      [ Start Draw ]       │
└─────────────────────────────────────────────────────┘

### Mobile Interface
![Mobile View](docs/mockups/mobile_view.md)
┌──────────────────────────────┐
│ [ START DRAW ]               │
├──────────────────────────────┤
│    jo****<EMAIL>       │
│    Won Rp5,000,000           │
└──────────────────────────────┘

[View all mockups in docs/mockups/]

## Documentation

- [Admin Guide](docs/admin.md)
- [Technical Architecture](docs/architecture.md)
- [API Reference](docs/api.md)

## Project Status

✅ Core features implemented (v1.0)
📝 Documentation in progress
✨ Optional enhancements planned
